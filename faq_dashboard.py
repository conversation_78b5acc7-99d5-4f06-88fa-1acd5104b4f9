#!/usr/bin/env python3
"""
FAQ Dashboard - Streamlit Application

This dashboard displays the FAQ.csv file generated by the faq_build.py script.
It provides an interactive interface to browse through FAQ clusters with
expandable sections for detailed source questions and answers.

Usage:
    streamlit run faq_dashboard.py
"""
import argparse

import streamlit as st
import pandas as pd
import os
from typing import Optional
import ast


@st.cache_data
def load_faq_data(file_path: str = "FAQ.csv") -> Optional[pd.DataFrame]:
    """
    Load FAQ data from CSV file with error handling.

    Args:
        file_path: Path to the FAQ CSV file

    Returns:
        DataFrame with FAQ data or None if loading fails
    """
    try:
        if not os.path.exists(file_path):
            return None

        # Load the CSV with proper handling for complex text fields
        df = pd.read_csv(
            file_path,
            quoting=1,  # QUOTE_ALL
            escapechar='\\',
            on_bad_lines='warn'
        )

        return df

    except Exception as e:
        st.error(f"Error loading FAQ data: {str(e)}")
        return None


def parse_list_column(value) -> list:
    """
    Parse string representation of list back to actual list.

    Args:
        value: String representation of a list or actual list

    Returns:
        Parsed list or empty list if parsing fails
    """
    if pd.isna(value):
        return []

    if isinstance(value, list):
        return value

    try:
        # Try to parse as Python literal (list)
        return ast.literal_eval(value)
    except (ValueError, SyntaxError):
        # If all parsing fails, treat as single string
        return [str(value)]


def display_faq_entry(row: pd.Series, index: int):
    """
    Display a single FAQ entry with expandable sections.

    Args:
        row: DataFrame row containing FAQ data
        index: Row index for unique keys
    """
    # Main FAQ display
    st.markdown(f"### FAQ #{row['cluster_id']}")
    st.markdown(f"**Question:** {row['canonical_question']}")
    st.markdown(f"**Answer:** {row['consolidated_answer']}")
    st.markdown(f"**Sources:** {row['source_count']} Q&A pairs consolidated")

    # Expandable sections for source data
    if 'questions' in row and 'answers' in row and pd.notna(row['questions']) and pd.notna(row['answers']):
        col1, col2 = st.columns(2)

        with col1:
            with st.expander(f"📝 Original Questions ({row['source_count']})", expanded=False):
                questions = parse_list_column(row['questions'])
                if questions:
                    for i, question in enumerate(questions, 1):
                        st.markdown(f"**{i}.** {question}")
                else:
                    st.info("No original questions available")

        with col2:
            with st.expander(f"💬 Original Answers ({row['source_count']})", expanded=False):
                answers = parse_list_column(row['answers'])
                if answers:
                    for i, answer in enumerate(answers, 1):
                        st.markdown(f"**{i}.** {answer}")
                else:
                    st.info("No original answers available")

    st.divider()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('-f', '--file', default='FAQ.csv', help='Path to the FAQ CSV file')
    return parser.parse_args()


def main():
    """Main dashboard application."""
    # Page configuration
    args = parse_args()
    st.set_page_config(
        page_title="FAQ Dashboard",
        page_icon="❓",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Main title
    st.title("❓ FAQ Dashboard")
    st.markdown("Interactive dashboard for browsing FAQ clusters generated from Q&A data")

    # Load data
    df = load_faq_data(args.file)
    if df is None:
        st.error("FAQ file not found: FAQ.csv")
        st.info("Please ensure FAQ.csv is in the current directory.")
        st.stop()

    # Sidebar filters and controls
    st.sidebar.header("🔍 Filters & Controls")

    # Search functionality
    search_term = st.sidebar.text_input(
        "Search FAQs",
        placeholder="Enter keywords to search questions or answers..."
    )

    # Source count filter
    min_sources = st.sidebar.slider(
        "Minimum Source Count",
        min_value=int(df['source_count'].min()),
        max_value=int(df['source_count'].max()),
        value=int(df['source_count'].min())
    )

    # Pagination settings
    st.sidebar.header("📄 Pagination")
    items_per_page = st.sidebar.selectbox(
        "Items per page",
        options=[5, 10, 20],
        index=1,  # Default to 10
        help="Number of FAQ entries to display per page"
    )

    # Apply filters
    filtered_df = df.copy()

    if min_sources > df['source_count'].min():
        filtered_df = filtered_df[filtered_df['source_count'] >= min_sources]

    if search_term:
        mask = (
            filtered_df['canonical_question'].str.contains(search_term, case=False, na=False) |
            filtered_df['consolidated_answer'].str.contains(search_term, case=False, na=False)
        )
        filtered_df = filtered_df[mask]

    # Initialize session state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 1

    # Reset pagination when filters change
    filter_key = f"{search_term}_{min_sources}_{len(filtered_df)}"
    if 'last_filter_key' not in st.session_state or st.session_state.last_filter_key != filter_key:
        if 'last_filter_key' in st.session_state:
            st.session_state.current_page = 1
        st.session_state.last_filter_key = filter_key

    # Display summary
    st.header("📊 Summary Statistics")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total FAQ Entries", len(df))

    with col2:
        total_sources = df['source_count'].sum()
        st.metric("Total Source Q&As", total_sources)

    with col3:
        avg_sources = df['source_count'].mean()
        st.metric("Avg Sources per FAQ", f"{avg_sources:.1f}")

    with col4:
        max_sources = df['source_count'].max()
        st.metric("Max Sources in FAQ", max_sources)

    # Display results count
    if len(filtered_df) != len(df):
        st.info(f"Showing {len(filtered_df)} of {len(df)} FAQ entries")

    # Main content area
    st.header("📋 FAQ Entries")

    if len(filtered_df) == 0:
        st.warning("No FAQ entries match your current filters.")
        return

    # Calculate pagination
    total_items = len(filtered_df)
    total_pages = (total_items - 1) // items_per_page + 1
    current_page = min(st.session_state.current_page, total_pages)

    # Navigation buttons
    nav_col1, nav_col2, nav_col3, nav_col4, nav_col5 = st.columns(5)

    with nav_col1:
        if st.button("⏮️ First", disabled=(current_page == 1)):
            st.session_state.current_page = 1
            st.rerun()

    with nav_col2:
        if st.button("⬅️ Previous", disabled=(current_page == 1)):
            st.session_state.current_page = max(1, current_page - 1)
            st.rerun()

    with nav_col3:
        st.markdown(f"**Page {current_page} of {total_pages}**")

    with nav_col4:
        if st.button("➡️ Next", disabled=(current_page == total_pages)):
            st.session_state.current_page = min(total_pages, current_page + 1)
            st.rerun()

    with nav_col5:
        if st.button("⏭️ Last", disabled=(current_page == total_pages)):
            st.session_state.current_page = total_pages
            st.rerun()

    # Calculate slice for current page
    start_idx = (current_page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, total_items)

    # Get current page data
    current_page_df = filtered_df.iloc[start_idx:end_idx]

    # Display mode selection
    display_mode = st.radio(
        "Display Mode",
        ["Table View", "Detailed View"],
        horizontal=True
    )

    # Show current page info
    st.info(f"Showing entries {start_idx + 1}-{end_idx} of {total_items}")

    if display_mode == "Table View":
        # Table view - show main columns only
        display_columns = ['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count']
        st.dataframe(
            current_page_df[display_columns],
            use_container_width=True,
            hide_index=True,
            column_config={
                'cluster_id': st.column_config.NumberColumn('Cluster ID', width='small'),
                'canonical_question': st.column_config.TextColumn('Question', width='large'),
                'consolidated_answer': st.column_config.TextColumn('Answer', width='large'),
                'source_count': st.column_config.NumberColumn('Sources', width='small')
            }
        )
    else:
        # Detailed view with expandable sections - only show current page
        for index, row in current_page_df.iterrows():
            display_faq_entry(row, index)


if __name__ == "__main__":
    main()
