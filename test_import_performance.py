#!/usr/bin/env python3
"""
Performance test script for import_history function optimization.
Compares optimized vs original import_history functions for performance and data consistency.
"""
import asyncio
import io
import os
import sys
import time
from typing import Dict

import pandas as pd
import sqlalchemy as sa
from sqlalchemy import select, and_

from ira_chat.db import models

# Set up database environment variables from memory
os.environ['PGPASSWORD'] = 'passwd'
os.environ['PGUSER'] = 'postgres'
os.environ['PGHOST'] = 'localhost'
os.environ['PGDATABASE'] = 'ira_prod'
os.environ['PGPORT'] = '5432'
os.environ['DB_TYPE'] = 'postgresql'

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/projects/ira-chat-api')

from ira_chat.db import api as db_api
from ira_chat.db import base
from ira_chat.api import chats


async def load_test_data(num_rows=50):
    """Load test data from CSV file"""
    df = pd.read_csv('import_history_many_graded.csv')
    df = df.head(num_rows)

    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_buffer.seek(0)

    return csv_buffer.getvalue().encode('utf-8'), df


async def test_import_function(csv_data, function_name, import_func):
    """Test a specific import function and return performance metrics"""

    print(f"\n=== Testing {function_name} ===")

    # Create a mock file upload
    class MockFile:
        def __init__(self, data):
            self.file = io.BytesIO(data)
            self.filename = "test.csv"

    mock_file = MockFile(csv_data)

    workspace = await db_api.get_workspace_by_id(44)
    org = await db_api.get_org_by_id(workspace.org_id)

    # Patch the context functions
    original_current_workspace = chats.ctx.current_workspace
    original_current_org = chats.ctx.current_org
    original_current_session = chats.ctx.current_session
    original_check_permission = chats._check_permission

    chats.ctx.current_workspace = lambda: workspace
    chats.ctx.current_org = lambda: org
    chats.ctx.current_session = lambda: models.Session(user_id=5)
    chats._check_permission = lambda: asyncio.sleep(0)  # Mock async function
    # mock calls to chats.index_provided_answers to avoid actual indexing
    chats.index_provided_answers = lambda *args, **kwargs: asyncio.sleep(0)

    try:
        start_time = time.time()

        # Run the import
        result = await import_func(mock_file, ',')

        end_time = time.time()
        duration = end_time - start_time

        print(f"Import completed in {duration:.3f} seconds")
        print(f"Result: {result.body.decode() if hasattr(result, 'body') else 'Success'}")

        return duration, True

    except Exception as e:
        print(f"Error during import: {e}")
        import traceback
        traceback.print_exc()
        return None, False

    finally:
        # Restore original functions
        chats.ctx.current_workspace = original_current_workspace
        chats.ctx.current_org = original_current_org
        chats.ctx.current_session = original_current_session
        chats._check_permission = original_check_permission


async def capture_database_state(df: pd.DataFrame, workspace_id: int):
    """Capture the current state of database records for comparison"""
    async with base.session_context() as session:
        # Get current state of messages
        message_ids = df['id_question'].tolist()
        messages = await db_api.list_all_chat_messages(workspace_id=workspace_id, ids=message_ids)
        message_state = {m.id: {'tags': m.tags} for m in messages}

        # Get current state of outputs
        output_ids = df['id_answer'].tolist()
        outputs = await db_api.list_all_chat_message_outputs(workspace_id=workspace_id, ids=output_ids)
        output_state = {o.id: {
            'grade_selector': o.grade_selector,
            'rating': o.rating,
            'note': o.note
        } for o in outputs}

        # Get current state of provided answers
        provided_answers = await db_api.list_provided_answers(
            workspace_id=workspace_id,
            object_ids=message_ids
        )
        provided_answer_state = {pa.object_id: pa.answer for pa in provided_answers}

        # Get current metrics state - comprehensive capture using direct queries
        # Focus on grade_correctness metrics and tag updates
        metrics_state = {}

        # Get grade_correctness metrics for outputs
        correctness_query = select(models.Metric).where(
            and_(
                models.Metric.workspace_id == workspace_id,
                models.Metric.type == 'message',
                models.Metric.app_type == 'chat',
                models.Metric.object_id.in_([str(oid) for oid in output_ids])
            )
        )
        correctness_result = await session.execute(correctness_query)
        correctness_metrics = correctness_result.scalars().fetchall()

        for metric in correctness_metrics:
            key = f"correctness_{metric.object_id}"
            metrics_state[key] = {
                'type': metric.type,
                'value': metric.value,
                'tag': metric.tag,
                'object_id': metric.object_id,
                'app_type': metric.app_type
            }

        # Get tag update metrics for messages
        tag_query = select(models.Metric).where(
            and_(
                models.Metric.workspace_id == workspace_id,
                models.Metric.type == 'tag_update',
                models.Metric.app_type == 'chat',
                models.Metric.object_id.in_([str(mid) for mid in message_ids])
            )
        )
        tag_result = await session.execute(tag_query)
        tag_metrics = tag_result.scalars().fetchall()

        for metric in tag_metrics:
            key = f"tag_{metric.object_id}"
            metrics_state[key] = {
                'type': metric.type,
                'value': metric.value,
                'tag': metric.tag,
                'object_id': metric.object_id,
                'app_type': metric.app_type
            }

        return {
            'messages': message_state,
            'outputs': output_state,
            'provided_answers': provided_answer_state,
            'metrics': metrics_state
        }


async def compare_database_states(state1: Dict, state2: Dict, test_name: str):
    """Compare two database states and report differences"""
    print(f"\n=== Data Consistency Check: {test_name} ===")

    differences = []

    # Compare messages
    for msg_id in set(state1['messages'].keys()) | set(state2['messages'].keys()):
        s1_msg = state1['messages'].get(msg_id, {})
        s2_msg = state2['messages'].get(msg_id, {})
        if s1_msg != s2_msg:
            differences.append(f"Message {msg_id}: {s1_msg} vs {s2_msg}")

    # Compare outputs
    for out_id in set(state1['outputs'].keys()) | set(state2['outputs'].keys()):
        s1_out = state1['outputs'].get(out_id, {})
        s2_out = state2['outputs'].get(out_id, {})
        if s1_out != s2_out:
            differences.append(f"Output {out_id}: {s1_out} vs {s2_out}")

    # Compare provided answers
    for obj_id in set(state1['provided_answers'].keys()) | set(state2['provided_answers'].keys()):
        s1_pa = state1['provided_answers'].get(obj_id)
        s2_pa = state2['provided_answers'].get(obj_id)
        if s1_pa != s2_pa:
            differences.append(f"Provided Answer {obj_id}: {s1_pa} vs {s2_pa}")

    # Compare metrics (comprehensive comparison including grade_correctness and tag updates)
    all_metric_keys = set(state1['metrics'].keys()) | set(state2['metrics'].keys())

    for metric_key in all_metric_keys:
        s1_metric = state1['metrics'].get(metric_key)
        s2_metric = state2['metrics'].get(metric_key)

        if s1_metric is None and s2_metric is not None:
            differences.append(f"Metric {metric_key}: Missing in original vs {s2_metric}")
        elif s1_metric is not None and s2_metric is None:
            differences.append(f"Metric {metric_key}: {s1_metric} vs Missing in optimized")
        elif s1_metric != s2_metric:
            # Deep comparison for metric dictionaries
            if isinstance(s1_metric, dict) and isinstance(s2_metric, dict):
                for field in set(s1_metric.keys()) | set(s2_metric.keys()):
                    s1_val = s1_metric.get(field)
                    s2_val = s2_metric.get(field)

                    if s1_val != s2_val:
                        # Check if it's a small floating point difference for numeric values
                        if isinstance(s1_val, (int, float)) and isinstance(s2_val, (int, float)):
                            if abs(s1_val - s2_val) > 0.001:
                                differences.append(f"Metric {metric_key}.{field}: {s1_val} vs {s2_val}")
                        else:
                            differences.append(f"Metric {metric_key}.{field}: {s1_val} vs {s2_val}")
            else:
                # Simple value comparison
                if isinstance(s1_metric, (int, float)) and isinstance(s2_metric, (int, float)):
                    if abs(s1_metric - s2_metric) > 0.001:
                        differences.append(f"Metric {metric_key}: {s1_metric} vs {s2_metric}")
                else:
                    differences.append(f"Metric {metric_key}: {s1_metric} vs {s2_metric}")

    if differences:
        print(f"❌ INCONSISTENCY DETECTED: {len(differences)} differences found:")
        for diff in differences[:10]:  # Show first 10 differences
            print(f"  - {diff}")
        if len(differences) > 10:
            print(f"  ... and {len(differences) - 10} more differences")
        return False
    else:
        print("✅ DATA CONSISTENCY: Both functions produced identical results")
        return True


async def main():
    """Main performance and consistency test"""

    print("="*80)
    print("IMPORT HISTORY PERFORMANCE & CONSISTENCY TEST")
    print("="*80)

    # Load test data
    print("Loading test data...")
    rows = 1000  # Test with larger dataset to see comprehensive metrics
    csv_data, df = await load_test_data(rows)
    print(f"Loaded {rows} rows from CSV file")

    workspace = await db_api.get_workspace_by_id(44)

    # Capture initial database state
    print("\nCapturing initial database state...")
    initial_state = await capture_database_state(df, workspace.id)

    print("\n" + "=" * 80)
    print("TESTING ORIGINAL IMPORT FUNCTION")
    print("=" * 80)

    # Reset to initial state by running the test again with original data
    # (This is a simplified approach - in production you'd want proper rollback)

    # Test original function
    original_duration, original_success = await test_import_function(
        csv_data, "Original Import", chats.import_history
    )

    # Capture state after original function
    if original_success:
        original_state = await capture_database_state(df, workspace.id)

    raise Exception("Stop here")
    # Clean up provided answers and metrics created by original function
    async with base.session_context() as session:
        await session.execute(sa.text("DELETE FROM provided_answers where created_at > '2025-08-02'"))
        await session.execute(sa.text("UPDATE metrics set tag = null where created_at > '2025-08-03'"))

    print("\n" + "=" * 80)
    print("TESTING OPTIMIZED IMPORT FUNCTION")
    print("=" * 80)

    # Test optimized function
    optimized_duration, optimized_success = await test_import_function(
        csv_data, "Optimized Import", chats.import_history
    )

    # Capture state after optimized function
    if optimized_success:
        optimized_state = await capture_database_state(df, workspace.id)

    print("\n" + "="*80)
    print("PERFORMANCE COMPARISON")
    print("="*80)

    if optimized_success and original_success:
        print(f"Optimized function: {optimized_duration:.3f} seconds")
        print(f"Original function:  {original_duration:.3f} seconds")

        if optimized_duration and original_duration:
            speedup = original_duration / optimized_duration
            print(f"Performance improvement: {speedup:.1f}x faster")

            # Extrapolate to larger datasets
            rows_per_second_opt = rows / optimized_duration
            rows_per_second_orig = rows / original_duration

            print(f"\nThroughput:")
            print(f"Optimized: {rows_per_second_opt:.1f} rows/second")
            print(f"Original:  {rows_per_second_orig:.1f} rows/second")

        # Data consistency check (original state as reference)
        if optimized_success and original_success:
            consistency_ok = await compare_database_states(
                original_state, optimized_state, "Original vs Optimized"
            )

            if consistency_ok:
                print("\n✅ SUCCESS: Optimized function is faster AND produces identical results!")
            else:
                print("\n❌ WARNING: Performance improved but data consistency issues detected!")

    else:
        print("❌ One or both functions failed - cannot compare performance")

    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Test dataset: {rows} rows")
    print(f"Optimized function: {'✅ Success' if optimized_success else '❌ Failed'}")
    print(f"Original function:  {'✅ Success' if original_success else '❌ Failed'}")

    if optimized_success and original_success and optimized_duration and original_duration:
        speedup = original_duration / optimized_duration
        print(f"Performance improvement: {speedup:.1f}x")

        if speedup > 2:
            print("🚀 EXCELLENT: Significant performance improvement!")
        elif speedup > 1.5:
            print("✅ GOOD: Noticeable performance improvement")
        elif speedup > 1.1:
            print("⚠️  MINOR: Small performance improvement")
        else:
            print("❌ POOR: No significant performance improvement")


if __name__ == "__main__":
    asyncio.run(main())
