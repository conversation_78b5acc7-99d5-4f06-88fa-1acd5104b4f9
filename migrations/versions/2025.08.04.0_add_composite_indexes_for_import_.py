"""Add composite indexes for import_history optimization

Revision ID: 2025.08.04.0
Revises: 2025.08.03.0
Create Date: 2025-08-04 15:19:24.311502

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '2025.08.04.0'
down_revision: Union[str, None] = '2025.08.03.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_chat_suggestion_message_id', table_name='chat_suggestions', if_exists=True)
    op.create_index(op.f('ix_chat_suggestions_chat_id'), 'chat_suggestions', ['chat_id'], unique=False, if_not_exists=True)
    op.create_index(op.f('ix_chat_suggestions_message_id'), 'chat_suggestions', ['message_id'], unique=False, if_not_exists=True)
    op.drop_index('idx_metrics_workspace_updated_type', table_name='metrics', postgresql_include=['value'], if_exists=True)
    op.alter_column('organization_domains', 'domain',
                    existing_type=sa.VARCHAR(length=200),
                    nullable=False)
    op.alter_column('organization_domains', 'org_id',
                    existing_type=sa.INTEGER(),
                    nullable=False)
    op.drop_index('ix_organization_domains_domain', table_name='organization_domains', if_exists=True)
    op.alter_column('organizations', 'description',
                    existing_type=sa.VARCHAR(length=200),
                    type_=sa.String(length=255),
                    existing_nullable=True)

    # ### Custom indexes for import_history optimization ###

    # 1. Composite index for chat_messages (chat_id, id) to optimize list_all_chat_messages
    # This optimizes: WHERE chats.workspace_id = ? AND chat_messages.chat_id = chats.id AND chat_messages.id IN (?)
    op.create_index('idx_chat_messages_chat_id_id', 'chat_messages', ['chat_id', 'id'], unique=False)

    # 2. Composite index for chat_message_outputs (chat_id, id) to optimize list_all_chat_message_outputs
    # This optimizes: WHERE chats.workspace_id = ? AND chat_message_outputs.chat_id = chats.id AND chat_message_outputs.id IN (?)
    op.create_index('idx_chat_message_outputs_chat_id_id', 'chat_message_outputs', ['chat_id', 'id'], unique=False)

    # 3. Composite index for provided_answers (workspace_id, app_id, object_id) to optimize bulk_get_provided_answers
    # This optimizes: WHERE (workspace_id = ? AND app_id = ? AND object_id = ?) OR ...
    op.create_index('idx_provided_answers_ws_app_obj', 'provided_answers', ['workspace_id', 'app_id', 'object_id'],
                    unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # ### Drop custom indexes for import_history optimization ###
    op.drop_index('idx_provided_answers_ws_app_obj', table_name='provided_answers')
    op.drop_index('idx_chat_message_outputs_chat_id_id', table_name='chat_message_outputs')
    op.drop_index('idx_chat_messages_chat_id_id', table_name='chat_messages')

    op.alter_column('organizations', 'description',
                    existing_type=sa.String(length=255),
                    type_=sa.VARCHAR(length=200),
                    existing_nullable=True)
    op.drop_constraint(None, 'organization_domains', type_='unique')
    op.create_index('ix_organization_domains_domain', 'organization_domains', ['domain'], unique=True)
    op.alter_column('organization_domains', 'org_id',
                    existing_type=sa.INTEGER(),
                    nullable=True)
    op.alter_column('organization_domains', 'domain',
                    existing_type=sa.VARCHAR(length=200),
                    nullable=True)
    op.create_index('idx_metrics_workspace_updated_type', 'metrics', ['workspace_id', 'updated_at', 'type'],
                    unique=False, postgresql_include=['value'])
    op.drop_index(op.f('ix_chat_suggestions_message_id'), table_name='chat_suggestions')
    op.drop_index(op.f('ix_chat_suggestions_chat_id'), table_name='chat_suggestions')
    op.create_index('ix_chat_suggestion_message_id', 'chat_suggestions', ['message_id'], unique=False)
    op.create_index('ix_chat_suggestion_created_at', 'chat_suggestions', ['created_at'], unique=False)
    op.create_index('ix_chat_suggestion_chat_id', 'chat_suggestions', ['chat_id'], unique=False)
    # ### end Alembic commands ###
