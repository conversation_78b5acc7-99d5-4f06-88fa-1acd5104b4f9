import asyncio
import collections
import datetime
import io
import json
import logging
import os
from collections import defaultdict
from typing import Optional, AsyncIterator, Literal, List

import chardet
import pandas as pd
from fastapi import APIRouter, HTTPException, UploadFile, Form
from fastapi.responses import ORJSONResponse, Response
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from ira_chat.api import common_utils
from ira_chat.config.shared_config import SharedConfig
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.exceptions import NotFoundException
from ira_chat.policies import policies
from ira_chat.services import engine_manager_chats, jobs, dataset_manager, dataset_utils
from ira_chat.services.dataset_manager import get_storage_dataset_file_name
from ira_chat.utils import utils, metric_utils

logger = logging.getLogger(__name__)
MESSAGES_ALLOWED_ORDER = {'id', 'created_at'}
# Configuration for timeouts
MAX_PROCESSING_TIMEOUT = int(os.environ.get('CHAT_PROCESSING_TIMEOUT', 300))  # 5 minutes default
MAX_STATUS_WAIT_TIMEOUT = int(os.environ.get('CHAT_STATUS_WAIT_TIMEOUT', 240))  # 4 minutes default
POLL_INTERVAL = 0.5


class ChatConfig(BaseModel):
    temperature: Optional[float] = None


class Chat(BaseModel):
    title: str
    description: Optional[str] = None
    config: Optional[dict] = None
    meta: Optional[dict] = None


class ChatUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None
    meta: Optional[dict] = None


class ChatMessage(BaseModel):
    content: str
    note: Optional[str] = None


class EditMessage(BaseModel):
    content: Optional[str] = None
    tags: Optional[list[str]] = None
    note: Optional[str] = None


class MessageChange(BaseModel):
    rating: Optional[int] = None
    provided_answer: Optional[str] = None
    result_id: Optional[int] = None
    note: Optional[str] = None
    grade_selector: Optional[str] = None
    tags: Optional[list[str]] = None


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'chats'))
    # router.include_router(chat_files.get_router())

    router.add_api_route("", list_chats, methods=['GET'], name='List chats')
    router.add_api_route("/metrics", list_chat_metrics, methods=['GET'], name='List chats metrics')
    router.add_api_route("/agg_metrics", agg_chat_metrics, methods=['GET'], name='Agg chats metrics')
    router.add_api_route("/graph", agg_chats_graph, methods=['GET'], name='Agg chats graph')
    router.add_api_route("/csv", download_csv, methods=['GET'], name='Get CSV of all chats')
    router.add_api_route("/history", download_history, methods=['GET'], name='Get history of all chats')
    router.add_api_route("/history_import", import_history, methods=['POST'], name='Import history')
    router.add_api_route("", create_chat, methods=['POST'], name='Create chat')
    router.add_api_route("/{chat_id}", get_chat, methods=['GET'], name='Get chat')
    router.add_api_route("/{chat_id}/default_prompt_config", prompt_config, methods=['GET'],
                         name='Get chat prompt config')
    router.add_api_route("/{chat_id}", update_chat, methods=['PUT'], name='Update chat')
    router.add_api_route("/{chat_id}", delete_chat, methods=['DELETE'], name='Delete chat')
    router.add_api_route("/{chat_id}/csv", download_csv, methods=['GET'], name='Get CSV of a chat')
    router.add_api_route("/{chat_id}/clear", clear_chat, methods=['DELETE'], name='Clear chat')
    router.add_api_route("/{chat_id}/delete", delete_messages, methods=['DELETE'], name='Delete last N messages')
    router.add_api_route("/{chat_id}/regenerate", regenerate_message, methods=['PUT'], name='Regenerate chat answer')
    router.add_api_route("/{chat_id}/regenerate_stream", regenerate_message_stream, methods=['PUT'],
                         name='Regenerate chat answer (streaming)')
    router.add_api_route("/{chat_id}/rollback", rollback_message, methods=['PUT'], name='Rollback chat to 1 step back')
    router.add_api_route("/{chat_id}/graph", get_chat_graph, methods=['GET'], name='Get chat graph metrics')
    router.add_api_route("/{chat_id}/doc_search", chat_doc_search, methods=['GET'], name='Search docs by query sample')
    router.add_api_route("/{chat_id}/messages_metrics", get_messages_metrics, methods=['GET'],
                         name='Get all messages metrics')
    router.add_api_route("/{chat_id}/messages", list_messages, methods=['GET'], name='List messages')
    router.add_api_route("/{chat_id}/messages", create_message, methods=['POST'], name='Post a message')
    router.add_api_route("/{chat_id}/messages_form", create_message_form, methods=['POST'], name='Post a message')
    router.add_api_route("/{chat_id}/messages_stream", create_message_stream, methods=['POST'],
                         name='Post a message (streaming)')
    router.add_api_route("/{chat_id}/messages_stream_form", create_message_stream_form, methods=['POST'],
                         name='Post a message (streaming)')
    router.add_api_route("/{chat_id}/messages/{message_id}", get_message, methods=['GET'], name='Get message')
    router.add_api_route("/{chat_id}/messages/{message_id}/metrics", get_message_metrics, methods=['GET'],
                         name='Get message metrics')
    router.add_api_route("/{chat_id}/messages/{message_id}", edit_message, methods=['PUT'], name='Edit a message')

    router.add_api_route(
        "/{chat_id}/messages/{message_id}/change", change_message, methods=['PUT'], name='Edit a message'
    )
    router.add_api_route(
        "/{chat_id}/messages/{message_id}", delete_message, methods=['DELETE'], name='Delete a message'
    )

    return router


additional_metrics = {'message_count': 'count'}
config_no_change_keys = [
    'input_rail_enabled',
    'output_rail_enabled',
    'no_answer_template',
    'no_answer_strict',
    'settings',
    'index_type',
]


async def list_chats(
    limit: int = 100,
    page: Optional[int] = None,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    q_items: str = None,
    q_tags: str = None,
    q_grade_selector: str = None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    page = page or 1
    params = dict(
        workspace_id=ws.id,
        limit=limit,
        page=page,
        order=order,
        desc=desc,
        q=q,
        metric_agg_func=common_utils.get_available_metrics().get(order.split('.')[-1], None),
        metric_start_date=start_date,
        metric_end_date=end_date,
        q_items=q_items,
        q_tags=q_tags,
        q_grade_selector=q_grade_selector,
    )

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    async with base.session_context():
        if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW):
            chats, count = await db_api.list_chats(owner_id=ses.user_id, **params)
            chat_dicts = [c.to_dict() for c in chats]
        else:
            chats, count = await db_api.list_chats(**params)
            chat_dicts = [c.to_dict() for c in chats]
            user_ids = list(set([c.owner_id for c in chats]))
            users = await db_api.list_users(user_ids) if user_ids else []
            user_map = {u.id: u for u in users}

            for chat in chat_dicts:
                if chat['owner_id'] in user_map:
                    chat['owner_login'] = user_map[chat['owner_id']].login
                    chat['owner_name'] = user_map[chat['owner_id']].name

    # import random
    # await asyncio.sleep(random.random() * 1000 % 100 / 1000)

    return ORJSONResponse(
        content={'items': chat_dicts, 'count': count, "page": page, "limit": limit},
    )


async def _list_chat_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = 'message,message_count,message_deleted',
    limit: Optional[int] = 100,
    page: Optional[int] = None,
    order: str = 'id',
    desc: bool = False,
    insert_missing_values: str = '',
    q: str = None,
    q_items: str = None,
    q_tags: str = None,
    q_grade_selector: str = None,
):
    insert_missing_values = common_utils.insert_missing_values_val(insert_missing_values)
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(
        workspace_id=ws.id,
        limit=limit,
        page=page or 1,
        order=order,
        desc=desc,
        q=q,
        q_items=q_items,
        q_tags=q_tags,
        q_grade_selector=q_grade_selector,
    )
    if not see_all:
        params['owner_id'] = ses.user_id

    if not start_date:
        start_date = models.now() - datetime.timedelta(days=1)
    if not end_date:
        end_date = models.now()

    metrics = [m.strip() for m in metrics.split(',')]
    event_metrics = common_utils.get_available_metrics()

    event_metrics.update(additional_metrics)
    for metric in metrics:
        if metric not in event_metrics:
            raise HTTPException(400, f'Metric {metric} does not exist')

    result = {
        'start_date': base.date_to_string(start_date),
        'end_date': base.date_to_string(end_date),
    }
    filtered_metrics = {m: f for m, f in event_metrics.items() if m in metrics}
    total_metrics = filtered_metrics.copy()
    total_metrics['chat_created'] = event_metrics['chat_created']
    total_metrics['chat_deleted'] = event_metrics['chat_deleted']

    async with base.session_context():
        apps, _ = await db_api.list_chats(**params)
        apps_map = {app.id: app for app in apps}

        per_app_id = defaultdict(dict)
        metric_types = list(filtered_metrics.keys())
        total_list = await db_api.get_metrics(
            [ws.id], start_date=start_date, end_date=end_date, metric_types=metric_types,
            app_type=models.APP_TYPE_CHAT,
            type_groups=filtered_metrics, agg_groups=['app_id']  # , 'type']
        )
        metrics_in_result = {t['type'] for t in total_list}
        for t in total_list:
            app_id = t['app_id']
            metric, value = t['type'], t['value']
            if app_id in apps_map:
                per_app_id[app_id][metric] = {'type': filtered_metrics[metric], 'total': value}
        for app_id in apps_map:
            if app_id in per_app_id:
                absent_metrics = metrics_in_result - set(per_app_id[app_id].keys())
                for m in absent_metrics:
                    per_app_id[app_id][m] = {'type': filtered_metrics[m], 'total': insert_missing_values}
            else:
                for metric in metric_types:
                    if metric in metrics_in_result:
                        per_app_id[app_id][metric] = {'type': filtered_metrics[metric], 'total': insert_missing_values}

        totals = await get_agg_metrics(ws.id, start_date, end_date, total_metrics)

    for app in apps:
        per_app_id[app.id]['last_activity'] = {'type': 'datetime', 'value': app.metrics.get('last_activity')}
        per_app_id[app.id]['message_count'] = {'type': 'count', 'value': app.metrics.get('message_count', 0)}

    # Squash to list
    per_app_id = [{'app_id': app_id, 'metrics': v} for app_id, v in per_app_id.items()]
    # Enrich data
    for app_metrics in per_app_id:
        app_metrics['title'] = apps_map.get(app_metrics['app_id']).title if apps_map.get(
            app_metrics['app_id']) else None

    result['metrics'] = per_app_id
    result['totals'] = totals['totals']

    return result


async def list_chat_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = 'message,message_count,message_deleted',
    limit: int = 100,
    page: Optional[int] = None,
    order: str = 'id',
    desc: bool = False,
    insert_missing_values: str = '',
    q: str = None,
    q_items: str = None,
    q_tags: str = None,
    q_grade_selector: str = None,
):
    result = await _list_chat_metrics(
        start_date=start_date,
        end_date=end_date,
        metrics=metrics,
        limit=limit,
        page=page,
        order=order,
        desc=desc,
        insert_missing_values=insert_missing_values,
        q=q,
        q_items=q_items,
        q_tags=q_tags,
        q_grade_selector=q_grade_selector,
    )
    return ORJSONResponse(content=result, status_code=200)


async def agg_chat_metrics(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: list | str = 'message',
):
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(403, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(workspace_id=ws.id)
    if not see_all:
        params['owner_id'] = ses.user_id

    if not start_date:
        start_date = models.now() - datetime.timedelta(days=1)
    if not end_date:
        end_date = models.now()

    metrics = [m.strip() for m in metrics.split(',')] if isinstance(metrics, str) else metrics
    event_metrics = common_utils.get_available_metrics()

    event_metrics.update(additional_metrics)
    for metric in metrics:
        if metric not in event_metrics:
            raise HTTPException(400, f'Metric {metric} does not exist')
    filtered_metrics = {m: f for m, f in event_metrics.items() if m in metrics}

    result = await get_agg_metrics(ws.id, start_date, end_date, filtered_metrics)

    return ORJSONResponse(content=result, status_code=200)


async def agg_chats_graph(
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    metrics: str = None,
    existing_only: bool = False,
    insert_missing_values: str = '0',
    q: str = None,
    q_items: str = None,
    q_tags: str = None,
    q_grade_selector: str = None,
):
    insert_missing_values = common_utils.insert_missing_values_val(insert_missing_values)
    if not metrics:
        metrics = list(common_utils.get_available_metrics().keys())

    ws = ctx.current_workspace()
    filtered_metrics = {m: f for m, f in common_utils.get_available_metrics().items() if m in metrics}
    async with base.session_context():
        total_list = await db_api.get_metrics(
            [ws.id], start_date=start_date, end_date=end_date, metric_types=list(filtered_metrics.keys()),
            app_type=models.APP_TYPE_CHAT,
            type_groups=filtered_metrics, agg_groups=['app_id']
        )
        chats, _ = await db_api.list_chats(
            workspace_id=ws.id,
            q=q,
            q_items=q_items,
            q_tags=q_tags,
            q_grade_selector=q_grade_selector,
        )
        chat_map = {c.id: c for c in chats}

    if q or q_items or q_tags or q_grade_selector:
        # Imply existing_only
        existing_only = True

    # ==== Insert missing metrics
    metrics_in_result = {t['type'] for t in total_list}
    metrics_by_app = defaultdict(lambda: metrics_in_result.copy())
    for t in total_list:
        metrics_by_app[t['app_id']].discard(t['type'])
    for app_id, missing_metrics in metrics_by_app.items():
        for m in missing_metrics:
            total_list.append({'app_id': app_id, 'type': m, 'value': insert_missing_values})
    # ==== Done inserting missing metrics

    by_metric = defaultdict(list)
    for t in total_list:
        app_id = t['app_id']
        metric, value = t['type'], t['value']
        by_metric[metric].append({'app_id': app_id, 'value': value})

    result = []
    for type, metric_list in by_metric.items():
        metric_list = sorted(metric_list, key=lambda x: x['app_id'])
        item = {
            'type': type,
            'display_name': common_utils.to_display_name(type),
            'group': common_utils.metric_group(type),
            'points': [
                {
                    # 'created_at': p['updated_at'],
                    # 'timestamp': p['timestamp'],
                    'value': p['value'],
                    'object_id': p['app_id'],
                    # 'title': chat_map.get(p['app_id']).title if p['app_id'] in chat_map else None,
                    # 'object_id': p['object_id'],
                    # 'item_id': output_id_map.get(p['object_id']),
                    # 'extra': json.loads(p['extra']) if p['extra'] else None,
                }
                for p in metric_list
                if not existing_only or p['app_id'] in chat_map
            ],
        }
        result.append(item)

    metric_names = common_utils.sorted_metric_names(list(metrics_in_result), common_utils.metric_views['inner'])
    return ORJSONResponse(content={'metrics': result, 'metric_names': metric_names}, status_code=200)


async def get_agg_metrics(workspace_id, start_date, end_date, filtered_metrics: dict):
    result = {
        'start_date': base.date_to_string(start_date),
        'end_date': base.date_to_string(end_date),
    }
    if 'message_count' in filtered_metrics:
        filtered_metrics.pop('message_count')

    async with base.session_context():
        all_message_count = await db_api.get_ws_chat_message_count(workspace_id=workspace_id)

        totals = {'message_count': all_message_count}
        metric_types = list(filtered_metrics.keys())
        agg_groups = ['workspace_id']
        total_list = await db_api.get_metrics(
            [workspace_id], start_date=start_date, end_date=end_date, metric_types=metric_types,
            type_groups=filtered_metrics, agg_groups=agg_groups  # , 'type']
        )
        for t in total_list:
            metric = t['type']
            totals[metric] = t['value']
        # [{'count': 14.0, 'workspace_id': 27}]

    result['totals'] = totals
    return result


async def download_csv(chat_id: Optional[int] = None, include_context=False, delimiter=','):
    ws = ctx.current_workspace()
    await _check_permission(chat_id)

    messages, results = await _get_messages_and_results(ws.id, chat_id)
    history = _build_history(messages, results)
    df = pd.DataFrame(history)

    if not include_context and 'context' in df:
        df = df.drop(columns=['context'])

    csv_data = df.to_csv(index=False, sep=delimiter)
    now = datetime.datetime.now(datetime.timezone.utc)
    file_name = f'{ws.name}_chats_{now.year}-{now.month}-{now.day}.csv'
    return Response(
        csv_data,
        status_code=200,
        media_type='text/csv',
        headers={'Content-Disposition': f'attachment; filename="{file_name}"'}
    )


async def download_history(
    chat_id: Optional[int] = None,
    include_metrics: Optional[bool] = False,
    include_context: bool = False,
    include_urls: bool = False,
    format: Literal['csv', 'json', 'xlsx'] = 'csv',
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    order: str = 'created_at',
    desc: bool = True,
    inline: bool = False,
    csv_delimiter=','
):
    ws = ctx.current_workspace()
    async with base.session_context():
        await _check_permission(chat_id)

        messages, outputs = await _get_messages_and_results(ws.id, chat_id, start_date, end_date)
        messages = sorted(messages, key=lambda x: getattr(x, order), reverse=desc)

        history_dicts, cols = await _build_history_dicts(
            messages, outputs, include_metrics, include_context, include_urls
        )

    if format in ['csv', 'xlsx']:
        return await _generate_file_response(history_dicts, cols, format, csv_delimiter, ws, inline=inline)
    elif format == 'json':
        return ORJSONResponse(content=history_dicts, status_code=200)
    else:
        raise HTTPException(400, 'Invalid format: {format}, only csv, json and xlsx are supported')


async def import_history_optimized(file: UploadFile, csv_delimiter: str = Form(',')):
    ws = ctx.current_workspace()
    org = ctx.current_org()
    await _check_permission()

    if not file.filename.lower().endswith(('.csv', '.xlsx')):
        raise HTTPException(400, 'Only CSV and XLSX files are supported')

    if file.filename.lower().endswith('.xlsx'):
        df = pd.read_excel(file.file)
    else:
        # detect encoding
        file_data = file.file.read()
        encoding = chardet.detect(file_data)['encoding']
        try:
            df = pd.read_csv(io.BytesIO(file_data), delimiter=csv_delimiter, encoding=encoding)
        except:
            df = pd.read_csv(io.BytesIO(file_data), delimiter=csv_delimiter, encoding='utf-8')

    df = df.replace(float('nan'), None)

    history_list = df.to_dict(orient='records')
    new_provided_answers = []
    async with base.session_context():
        # Validate columns
        required_columns = ['id_question', 'id_answer', 'chat_id', 'question', 'answer', 'grade_selector', 'rating', 'note', 'corrected_answer']
        if not all(col in df.columns for col in required_columns):
            raise HTTPException(400, f'Missing required columns: {required_columns}')

        # Validate grade selectors first
        for item in history_list:
            grade_selector = item.get('grade_selector')
            if grade_selector and grade_selector not in metric_utils.grade_map:
                raise HTTPException(400, f'Invalid grade selector: {grade_selector}')

        all_outputs = await db_api.list_all_chat_message_outputs(workspace_id=ws.id, ids=[item['id_answer'] for item in history_list])
        # all_messages = await db_api.list_all_chat_messages(workspace_id=ws.id, ids=[item['id_question'] for item in history_list])
        output_map = {o.id: o for o in all_outputs}
        # message_map = {m.id: m for m in all_messages}
        chats = await db_api.list_chats_all(ids=[item['chat_id'] for item in history_list], workspace_id=ws.id)
        chat_map = {c.id: c for c in chats}

        # Verify chat workspace
        if not all(chat.workspace_id == ws.id for chat in chats):
            raise HTTPException(400, 'One or more chats do not belong to the current workspace')

        # Use optimized bulk import
        await _import_history_optimized(history_list, ws, org, output_map, chat_map, new_provided_answers)

    # Schedule provided answers indexing
    if new_provided_answers:
        # Use the last chat_id for manager initialization
        last_chat_id = history_list[-1]['chat_id'] if history_list else None
        if last_chat_id and last_chat_id in chat_map:
            manager = await engine_manager_chats.ChatEngineManager.init_async(
                ctx.current_org(), ws=ctx.current_workspace(), app=chat_map[last_chat_id]
            )
            t = asyncio.create_task(index_provided_answers(manager, new_provided_answers, caller_id=ctx.current_session().user_id))

    return ORJSONResponse(content={'message': 'History imported', "status": "OK"})


async def _import_history_optimized(history_list, ws, org, output_map, chat_map, new_provided_answers):
    """
    Optimized bulk import using batch operations instead of individual queries.
    """
    # Prepare bulk update data
    message_updates = []
    output_updates = []
    metrics_to_upsert = []
    provided_answer_lookups = []

    # Process all items and prepare bulk operations
    for item in history_list:
        message_id = item['id_question']
        output_id = item['id_answer']
        chat_id = item['chat_id']
        grade_selector = item.get('grade_selector')
        rating = item.get('rating')
        note = item.get('note')
        corrected_answer = item.get('corrected_answer')
        tags = item.get('tags').split(',') if item.get('tags') else None
        primary_tag = tags[0] if tags else None

        # Prepare message updates
        if tags:
            # Note: tags field is PostgresJSONB, SQLAlchemy should handle JSON serialization
            # but for bulk updates we need to be explicit
            message_updates.append({
                'id': message_id,
                'tags': tags  # SQLAlchemy should handle the JSON conversion
            })

        # Prepare output updates
        output_update = {'id': output_id}
        if grade_selector:
            output_update['grade_selector'] = grade_selector
        if rating:
            output_update['rating'] = rating
        if note:
            output_update['note'] = note

        if len(output_update) > 1:  # More than just 'id'
            output_updates.append(output_update)

        # Prepare metrics
        if grade_selector:
            _prepare_grade_metrics(
                metrics_to_upsert, org.id, ws.id, chat_id, output_id,
                grade_selector, output_map[output_id].updated_at
            )

        if rating:
            _prepare_rating_metrics(
                metrics_to_upsert, org.id, ws.id, chat_id, output_id,
                rating, output_map[output_id].updated_at
            )

        # Prepare tag metrics update
        if tags:
            metrics_to_upsert.append({
                'org_id': org.id,
                'workspace_id': ws.id,
                'app_id': chat_id,
                'app_type': models.APP_TYPE_CHAT,
                'object_id': str(output_id),
                'type': 'tag_update',  # Placeholder for bulk tag update
                'value': 1,
                'created_at': output_map[output_id].updated_at,
                'updated_at': output_map[output_id].updated_at,
            })

        # Prepare provided answers
        if corrected_answer:
            provided_answer_lookups.append({
                'workspace_id': ws.id,
                'app_id': chat_id,
                'object_id': message_id,
                'corrected_answer': corrected_answer,
                'question': item['question'],
                'output_id': output_id
            })

    # Execute bulk operations
    if message_updates:
        await db_api.bulk_update_messages(message_updates)

    if output_updates:
        await db_api.bulk_update_message_outputs(output_updates)

    # Handle provided answers in bulk
    if provided_answer_lookups:
        await _handle_provided_answers_bulk(provided_answer_lookups, new_provided_answers, org, ws, output_map)

    # Handle metrics in bulk (excluding tag updates which need special handling)
    real_metrics = [m for m in metrics_to_upsert if m['type'] != 'tag_update']
    if real_metrics:
        await db_api.bulk_upsert_metrics(real_metrics)

    # Handle tag updates using bulk operations instead of individual updates
    tag_updates = [m for m in metrics_to_upsert if m['type'] == 'tag_update']
    if tag_updates:
        await db_api.bulk_update_metric_tags(tag_updates)


def _prepare_grade_metrics(metrics_list, org_id, workspace_id, chat_id, output_id, grade_value, set_date):
    """Prepare grade metrics for bulk upsert"""
    grade_metric = metric_utils.grade_to_value(grade_value)

    if grade_metric is not None:
        # Correctness grade metric
        metrics_list.append({
            'org_id': org_id,
            'workspace_id': workspace_id,
            'app_id': chat_id,
            'app_type': models.APP_TYPE_CHAT,
            'object_id': str(output_id),
            'type': models.METRIC_CORRECTNESS_GRADE,
            'value': grade_metric,
            'created_at': set_date,
            'updated_at': set_date,
        })

        # Generic grade event metric
        metrics_list.append({
            'org_id': org_id,
            'workspace_id': workspace_id,
            'app_id': chat_id,
            'app_type': models.APP_TYPE_CHAT,
            'object_id': str(output_id),
            'type': models.METRIC_GRADE_EVENT,
            'value': 1,
            'created_at': set_date,
            'updated_at': set_date,
        })

    # Named grade event metric
    metrics_list.append({
        'org_id': org_id,
        'workspace_id': workspace_id,
        'app_id': chat_id,
        'app_type': models.APP_TYPE_CHAT,
        'object_id': str(output_id),
        'type': metric_utils.grade_to_event_name(grade_value),
        'value': 1,
        'created_at': set_date,
        'updated_at': set_date,
    })


def _prepare_rating_metrics(metrics_list, org_id, workspace_id, chat_id, output_id, rating_value, set_date):
    """Prepare rating metrics for bulk upsert"""
    if rating_value is None:
        return

    # Rating value metric
    metrics_list.append({
        'org_id': org_id,
        'workspace_id': workspace_id,
        'app_id': chat_id,
        'app_type': models.APP_TYPE_CHAT,
        'object_id': str(output_id),
        'type': models.METRIC_RATING_VALUE,
        'value': rating_value,
        'created_at': set_date,
        'updated_at': set_date,
    })

    # Rating event metric
    metrics_list.append({
        'org_id': org_id,
        'workspace_id': workspace_id,
        'app_id': chat_id,
        'app_type': models.APP_TYPE_CHAT,
        'object_id': str(output_id),
        'type': models.METRIC_RATING_EVENT,
        'value': 1,
        'created_at': set_date,
        'updated_at': set_date,
    })


async def _handle_provided_answers_bulk(lookups, new_provided_answers, org, ws, output_map):
    """Handle provided answers in bulk"""
    # Get all existing provided answers in one query
    existing_answers = await db_api.bulk_get_provided_answers(lookups)

    answers_to_create = []
    answers_to_update = []
    correction_metrics = []

    for lookup in lookups:
        key = (lookup['workspace_id'], lookup['app_id'], lookup['object_id'])
        existing_answer = existing_answers.get(key)
        corrected_answer = lookup['corrected_answer']

        if existing_answer and existing_answer.answer.strip() != corrected_answer.strip():
            # Update existing answer
            answers_to_update.append({
                'id': existing_answer.id,
                'answer': corrected_answer,
                'question': lookup['question'],
            })
            new_provided_answers.append(existing_answer)

        elif not existing_answer:
            # Create new answer
            new_answer_data = {
                'workspace_id': lookup['workspace_id'],
                'app_id': lookup['app_id'],
                'object_id': lookup['object_id'],
                'question': lookup['question'],
                'answer': corrected_answer,
            }
            answers_to_create.append(new_answer_data)

            # Prepare correction metric
            correction_metrics.append({
                'org_id': org.id,
                'workspace_id': ws.id,
                'app_id': lookup['app_id'],
                'app_type': models.APP_TYPE_CHAT,
                'object_id': str(lookup['output_id']),
                'type': models.METRIC_CORRECTED_ANSWER,
                'value': 1,
                'created_at': output_map[lookup['output_id']].updated_at,
                'updated_at': output_map[lookup['output_id']].updated_at,
            })

    # Execute bulk operations
    if answers_to_create:
        created_answers = await db_api.bulk_create_provided_answers(answers_to_create)
        new_provided_answers.extend(created_answers)

    if answers_to_update:
        await db_api.bulk_update_provided_answers(answers_to_update)
        new_provided_answers.extend([
            models.ProvidedAnswer(id=u['id'], answer=u['answer'], question=u['question']) for u in answers_to_update
        ])

    if correction_metrics:
        await db_api.bulk_upsert_metrics(correction_metrics)


async def import_history(file: UploadFile, csv_delimiter: str = Form(',')):
    ws = ctx.current_workspace()
    org = ctx.current_org()
    await _check_permission()

    if not file.filename.lower().endswith(('.csv', '.xlsx')):
        raise HTTPException(400, 'Only CSV and XLSX files are supported')

    if file.filename.lower().endswith('.xlsx'):
        df = pd.read_excel(file.file)
    else:
        # detect encoding
        file_data = file.file.read()
        encoding = chardet.detect(file_data)['encoding']
        try:
            df = pd.read_csv(io.BytesIO(file_data), delimiter=csv_delimiter, encoding=encoding)
        except:
            df = pd.read_csv(io.BytesIO(file_data), delimiter=csv_delimiter, encoding='utf-8')

    df = df.replace(float('nan'), None)

    history_list = df.to_dict(orient='records')
    new_provided_answers = []
    async with base.session_context():
        # Validate columns
        required_columns = ['id_question', 'id_answer', 'chat_id', 'question', 'answer', 'grade_selector', 'rating', 'note', 'corrected_answer']
        if not all(col in df.columns for col in required_columns):
            raise HTTPException(400, f'Missing required columns: {required_columns}')

        # Validate grade selectors first
        for item in history_list:
            grade_selector = item.get('grade_selector')
            if grade_selector and grade_selector not in metric_utils.grade_map:
                raise HTTPException(400, f'Invalid grade selector: {grade_selector}')

        all_outputs = await db_api.list_all_chat_message_outputs(workspace_id=ws.id, ids=[item['id_answer'] for item in history_list])
        # all_messages = await db_api.list_all_chat_messages(workspace_id=ws.id, ids=[item['id_question'] for item in history_list])
        output_map = {o.id: o for o in all_outputs}
        # message_map = {m.id: m for m in all_messages}
        chats = await db_api.list_chats_all(ids=[item['chat_id'] for item in history_list], workspace_id=ws.id)
        chat_map = {c.id: c for c in chats}

        # Verify chat workspace
        if not all(chat.workspace_id == ws.id for chat in chats):
            raise HTTPException(400, 'One or more chats do not belong to the current workspace')

        for item in history_list:
            # Process and update only grade_selector, rating, note, corrected_answer
            message_id = item['id_question']
            output_id = item['id_answer']
            chat_id = item['chat_id']
            grade_selector = item.get('grade_selector')
            rating = item.get('rating')
            note = item.get('note')
            corrected_answer = item.get('corrected_answer')
            tags = item.get('tags').split(',') if item.get('tags') else None

            message_updates = {'tags': tags}
            primary_tag = tags[0] if tags else None
            await db_api.update_message(models.ChatMessage(id=message_id), message_updates)

            if grade_selector or rating or note or corrected_answer:
                update_values = {
                    k: v for k, v in [
                        ('grade_selector', grade_selector), ('rating', rating), ('note', note)
                    ] if v
                }
                await db_api.update_message_output_by_id(output_id, update_values)
                # ensure metrics
                if grade_selector:
                    await metric_utils.ensure_grade_metric(
                        org.id, ws.id, chat_id, output_id, grade_selector, output_map[output_id].updated_at, tag=primary_tag
                    )
                if rating:
                    await metric_utils.ensure_rating_metric(
                        org.id, ws.id, chat_id, output_id, rating, output_map[output_id].updated_at, tag=primary_tag
                    )
            if tags:
                await db_api.update_metrics(
                    {'tag': primary_tag}, workspace_id=ws.id, app_id=chat_id, object_id=output_id, app_type=models.APP_TYPE_CHAT
                )

            if corrected_answer:
                current_correction = await db_api.get_provided_answer_by(workspace_id=ws.id, app_id=chat_id, object_id=message_id)
                if current_correction and current_correction.answer.strip() != corrected_answer.strip():
                    provided_answer = await db_api.update_provided_answer(current_correction, {'answer': corrected_answer})
                elif not current_correction:
                    provided_answer = await db_api.create_provided_answer({
                        'workspace_id': ws.id,
                        'app_id': chat_id,
                        'object_id': message_id,
                        'question': item['question'],
                        'answer': corrected_answer,
                    })
                    await metric_utils.ensure_metric(
                        ctx.current_org().id, ctx.current_workspace().id, app_id=chat_id, result_id=output_id,
                        type_=models.METRIC_CORRECTED_ANSWER,
                        value=1, set_date=output_map[output_id].updated_at,
                    )
                else:
                    provided_answer = None

                if provided_answer:
                    # TODO implement correction of correction:
                    # TODO gather all the corrections and index them all at once (only if correction update is made)
                    # TODO re-compile and re-index entire provided answers file
                    new_provided_answers.append(provided_answer)

    # Schedule provided answers indexing
    if new_provided_answers:
        manager = await engine_manager_chats.ChatEngineManager.init_async(
            ctx.current_org(), ws=ctx.current_workspace(), app=chat_map[chat_id]
        )
        t = asyncio.create_task(index_provided_answers(manager, new_provided_answers, caller_id=ctx.current_session().user_id))

    return ORJSONResponse(content={'message': 'History imported', "status": "OK"})


async def _check_permission(chat_id: Optional[int] = None):
    ws = ctx.current_workspace()
    if chat_id:
        await _get_chat(chat_id)
    else:
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
        if not all_chat_allowed:
            raise HTTPException(403, 'Forbidden to view')


async def _get_messages_and_results(workspace_id: int, chat_id: Optional[int], start_date: datetime.datetime = None, end_date: datetime.datetime = None):
    messages = await db_api.list_all_chat_messages(
        workspace_id=workspace_id,
        chat_id=chat_id,
        ignore_statuses=[models.STATUS_ERROR, models.STATUS_ERROR_REASON],
        start_date=start_date,
        end_date=end_date,
    )
    results = await db_api.list_last_message_outputs(
        workspace_id=workspace_id,
        chat_id=chat_id,
        ignore_statuses=[models.STATUS_ERROR, models.STATUS_ERROR_REASON],
        start_date=start_date,
        end_date=end_date,
    )
    return messages, results


def _build_history(messages, results):
    history = sorted(messages + results, key=lambda x: (x.chat_id, x.created_at,))
    return [c.to_dict() for c in history]


async def _build_history_dicts(
    messages, outputs,
    include_metrics: bool = False,
    include_context: bool = False,
    include_urls: bool = False,
):
    metrics = []
    metric_map_by_object_by_type = None
    if include_metrics:
        metrics, metric_map_by_object_by_type = await _get_metrics(outputs)

    provided_answers = await db_api.list_provided_answers(
        workspace_id=ctx.current_workspace().id,
        object_ids=[message.id for message in messages]
    )
    provided_answers_map = {o.object_id: o for o in provided_answers}
    all_chat_ids = {m.chat_id for m in messages}
    chats = await db_api.list_chats_all(ids=list(all_chat_ids))
    chat_map = {c.id: c for c in chats}

    output_map = {o.chat_message_id: o for o in outputs}
    history_dicts = []
    # Build list of columns except for metrics
    cols = [
        'id_question', 'id_answer', 'chat_id', 'chat_title', 'user_id', 'question', 'answer',
        'tags', 'grade_selector', 'rating', 'corrected_answer', 'note'
    ]
    if include_context:
        cols.append('context')
    if include_urls:
        cols.append('chat_url')
    cols.extend(['created_at', 'updated_at'])

    for m in messages:
        output = output_map.get(m.id)
        provided_answer = provided_answers_map.get(m.id)
        item = {
            'id_question': m.id,
            'id_answer': output.id if output else None,
            'chat_id': m.chat_id,
            'chat_title': chat_map[m.chat_id].title if m.chat_id in chat_map else None,
            'user_id': m.owner_id,
            'question': m.content,
            'answer': output.content if output else None,
            'tags': ','.join(m.tags) if m.tags else None,
            'grade_selector': output.grade_selector if output else None,
            'rating': output.rating if output else None,
            'corrected_answer': provided_answer.answer if provided_answer else None,
            'note': output.note if output else None,
        }

        if include_context:
            item['context'] = json.dumps(output.context) if output else None

        if include_urls:
            item['chat_url'] = generate_chat_url(ctx.current_workspace().id, m.chat_id)

        for metric_name in metrics:
            val = metric_map_by_object_by_type.get(str(output.id), {}).get(metric_name)
            if val:
                val = round(val, 4)
            item[metric_name] = val

        item['created_at'] = base.date_to_string(m.created_at)
        item['updated_at'] = base.date_to_string(output.updated_at)

        history_dicts.append(item)
    return history_dicts, cols


def generate_chat_url(workspace_id, chat_id):
    base_url = utils.get_base_url(ctx.request.value)
    # api_prefix = utils.API_PREFIX.removeprefix('/')
    # UI url, not API /ws/32/chats/700
    return os.path.join(base_url, f'ws/{workspace_id}/chats/{chat_id}')


async def _get_metrics(outputs):
    metrics = [
        models.METRIC_RELEVANCE, models.METRIC_CONTEXT_UTIL,
        models.METRIC_FAITHFULNESS, models.METRIC_WEIGHTED_EVAL,
        models.METRIC_CORRECTNESS_GRADE, models.METRIC_RATING_VALUE,
    ]
    metrics_dict = await common_utils.build_metrics_items_graph(
        ctx.current_workspace(), models.APP_TYPE_CHAT, None, metrics,
        object_ids=[o.id for o in outputs],
        object_values_addition_ids=[o.chat_message_id for o in outputs],
    )
    metric_map_by_object_by_type = defaultdict(dict)
    for m in metrics_dict['data']:
        for metric in m['metrics']:
            metric_map_by_object_by_type[m['id']][metric['type']] = metric['value']
    return metrics, metric_map_by_object_by_type


# noinspection PyTypeChecker
async def _generate_file_response(history_dicts, cols, format, csv_delimiter, ws, inline: bool = False):
    if not history_dicts:
        df = pd.DataFrame(columns=cols)
    else:
        df = pd.DataFrame(history_dicts).replace(float('nan'), None)
    ws = ctx.current_workspace()
    if format == 'csv':
        data = df.to_csv(index=False, sep=csv_delimiter)
        media_type = 'text/csv'
        file_extension = 'csv'
    else:
        buffer = io.BytesIO()
        writer = pd.ExcelWriter(buffer)
        sheet_name = 'Data'
        df.to_excel(writer, sheet_name=sheet_name, index=False, engine='xlsxwriter')

        gray_bold = writer.book.add_format({'bold': True, 'bg_color': '#D3D3D3', 'border': 1})
        row_colors = [
            writer.book.add_format({'bg_color': '#F0F0F0', 'border_color': '#b2b2b2', 'border': 1}),
            writer.book.add_format({'bg_color': '#FFFFFF', 'border_color': '#b2b2b2', 'border': 1}),
        ]
        row_colors_wrap = [
            writer.book.add_format({'bg_color': '#F0F0F0', 'border_color': '#b2b2b2', 'border': 1, 'text_wrap': True}),
            writer.book.add_format({'bg_color': '#FFFFFF', 'border_color': '#b2b2b2', 'border': 1, 'text_wrap': True}),
        ]
        row_colors_number = [
            writer.book.add_format({'bg_color': '#F0F0F0', 'border_color': '#b2b2b2', 'border': 1, 'num_format': '#0'}),
            writer.book.add_format({'bg_color': '#FFFFFF', 'border_color': '#b2b2b2', 'border': 1, 'num_format': '#0'}),
        ]

        cols = df.columns.tolist()
        for col_num, col_name in enumerate(cols):
            # Write column headers
            writer.sheets[sheet_name].write(0, col_num, col_name, gray_bold)

            if col_name == 'grade_selector':
                writer.sheets[sheet_name].data_validation(
                    1, col_num, len(df), col_num,
                    {'validate': 'list', 'source': list(metric_utils.grade_map.keys())}
                )
            if col_name == 'tags':
                available_tags = ws.get_config().get('apps_config', {}).get('chat', {}).get('available_tags', [])
                if available_tags:
                    writer.sheets[sheet_name].data_validation(
                        1, col_num, len(df), col_num,
                        {'validate': 'list', 'source': list(available_tags)}
                    )

            # Rotate row formats white / gray
            for row_num, value in enumerate(df[col_name].values, start=1):
                row_format = row_colors[row_num % 2]
                if col_name == 'user_id':
                    row_format = row_colors_number[row_num % 2]
                if col_name in ['question', 'answer', 'corrected_answer']:
                    row_format = row_colors_wrap[row_num % 2]
                writer.sheets[sheet_name].write(row_num, col_num, value, row_format)

        for col_idx, column in enumerate(cols):
            if column in ['question', 'answer', 'corrected_answer']:
                if column == 'question':
                    writer.sheets[sheet_name].set_column(col_idx, col_idx, 50)
                else:
                    writer.sheets[sheet_name].set_column(col_idx, col_idx, 75)
            else:
                column_width = max(df[column].astype(str).map(len).max(), len(column))
                writer.sheets[sheet_name].set_column(col_idx, col_idx, column_width)

        writer.close()

        data = buffer.getvalue()
        media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        file_extension = 'xlsx'

    now = datetime.datetime.now(datetime.timezone.utc)
    file_name = f'{ws.name}_chats_{now.year}-{now.month}-{now.day}.{file_extension}'
    disposition = 'inline' if inline else 'attachment'
    return Response(
        data,
        status_code=200,
        media_type=media_type,
        headers={'Content-Disposition': f'{disposition}; filename="{file_name}"'}
    )


async def create_chat(chat_req: Chat):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    chat_dict = chat_req.model_dump()
    chat_dict['owner_id'] = ses.user_id
    chat_dict['workspace_id'] = ws.id
    if chat_req.config:
        config = {k: v for k, v in chat_req.config.items() if k not in config_no_change_keys}
        chat_dict['config'] = config

    chat = await db_api.create_chat(chat_dict)
    await metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=chat.id, type_=models.METRIC_CHAT_CREATED
    )

    return ORJSONResponse(content=chat.to_dict(), status_code=200)


async def get_chat(chat_id: int):
    ws = ctx.current_workspace()
    chat, chat_dict = await _get_chat(chat_id, edit=False)

    # chat_dict['config'] = json_utils.dict_merge(ws.get_config(), chat_dict['config'])
    chat_checked = await dataset_utils.dataset_index_check(ws.org_id, chat)
    chat_dict['status'] = chat_checked.status

    return ORJSONResponse(content=chat_dict, status_code=200)


async def _get_chat(chat_id, edit=True) -> tuple[models.Chat, dict]:
    async with base.session_context():
        ses = ctx.current_session()
        ws = ctx.current_workspace()
        check_permission = policies.AccessMode.ALL_CHATS_MANAGE if edit else policies.AccessMode.ALL_CHATS_VIEW
        all_chats_allowed = policies.is_allowed(ctx.current_permissions(), check_permission)
        chat = await db_api.get_chat_by_id(chat_id)

        if ses.user_id != chat.owner_id and not all_chats_allowed:
            raise HTTPException(403, 'Forbidden')
        if ws.id != chat.workspace_id:
            raise HTTPException(404, 'Not found')

        chat_dict = chat.to_dict()
        if all_chats_allowed:
            owner = await db_api.get_user_by_id(chat.owner_id, notfoundok=True)
            if owner:
                chat_dict['owner_login'] = owner.login
                chat_dict['owner_name'] = owner.name

    return chat, chat_dict


async def prompt_config(chat_id: int):
    ses = ctx.current_session()

    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    chat = await db_api.get_chat_by_id(chat_id)

    if ses.user_id != chat.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')

    manager = engine_manager_chats.ChatEngineManager(ctx.current_org(), ctx.current_workspace(), chat.get_config())

    return ORJSONResponse(content={'prompt_config': manager.get_default_prompts()}, status_code=200)


async def update_chat(chat_id: int, chat_req: ChatUpdate):
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != chat.owner_id and not all_chat_allowed:
            raise HTTPException(403, 'Must be the owner to update')

        chat_dict = chat_req.model_dump(exclude_unset=True)
        if not chat_dict:
            raise HTTPException(400, 'Provide a data to update')

        if chat_req.config:
            old_config = chat.get_config()
            config = {k: v for k, v in chat_req.config.items() if k not in config_no_change_keys}
            chat_dict['config'] = config
            if old_config != config:
                chat = await dataset_utils.dataset_index_check(ctx.current_org().id, chat)

        chat = await db_api.update_chat(chat, chat_dict)

    return ORJSONResponse(content=chat.to_dict(), status_code=200)


async def delete_chat(chat_id: int):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    ws = ctx.current_workspace()

    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to delete')

    async with base.session_context():
        await db_api.delete_messages(chat_id)
        await db_api.delete_message_outputs(chat_id)
        await db_api.delete_suggestions_for_chat(chat_id=chat_id)
        await db_api.delete_chat(chat_id)

        await metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_CHAT_DELETED
        )
        await metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE_DELETED, value=chat.metrics.get('message_count', 0)
        )

    return Response(status_code=204)


async def clear_chat(chat_id: int):
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        ws = ctx.current_workspace()
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != chat.owner_id and not all_chat_allowed:
            raise HTTPException(403, 'Must be the owner to clear')

        await db_api.delete_messages(chat_id)
        await db_api.delete_suggestions_for_chat(chat_id=chat_id)
        await db_api.delete_message_outputs(chat_id)

        await metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE_DELETED, value=chat.metrics.get('message_count', 0)
        )
        await db_api.update_metrics_for_chat(chat)

    return Response(status_code=204)


async def list_messages(chat_id: int, start_id: int = None, limit: int = None, order: str = None, desc: bool = False):
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        all_view = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
        if ses.user_id != chat.owner_id and not all_view:
            raise HTTPException(403, 'Forbidden')

        if order is not None and order not in MESSAGES_ALLOWED_ORDER:
            raise HTTPException(400, f'order allowed: {MESSAGES_ALLOWED_ORDER}, got: {order}')
        if order is None:
            order = 'id'

        messages = await db_api.list_messages(chat_id, start_id=start_id, limit=limit, order_by=order, desc=desc)
        results = await db_api.list_message_outputs(chat_id)
        all_suggestions = await db_api.list_suggestions(chat_id, limit=limit * 3 if limit else limit)
        all_provided_answers = await db_api.list_provided_answers(
            workspace_id=ctx.current_workspace().id, app_id=chat_id, object_ids=[m.id for m in messages]
        )

    msg_dicts = [m.to_dict() for m in messages]
    result_dicts = [r.to_dict(skip_ids=True) for r in results]
    suggestion_map = collections.defaultdict(list)
    results_map = collections.defaultdict(list)
    for s in all_suggestions:
        suggestion_map[s.message_id].append(s.to_dict(skip_ids=True))
    for o_dict in result_dicts:
        o_dict['suggestions'] = suggestion_map[o_dict['id']]
        results_map[o_dict['chat_message_id']].append(o_dict)

    provided_answers_map = {a.object_id: a for a in all_provided_answers}
    for msg in msg_dicts:
        msg['results'] = results_map[msg['id']]
        if msg['id'] in provided_answers_map:
            msg['provided_answer'] = provided_answers_map[msg['id']].answer

    return ORJSONResponse(content=msg_dicts, status_code=200)


async def get_chat_graph(
    chat_id: int,
    metrics: str = None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
    existing_only: bool = False,
):
    ws = ctx.current_workspace()
    async with base.session_context():
        chat, _ = await _get_chat(chat_id, False)

        if metrics:
            metrics = [m.strip() for m in metrics.split(',')]
        else:
            skip_metrics = [models.METRIC_MESSAGE, models.METRIC_CHAT_CREATED, models.METRIC_CHAT_DELETED]
            metrics = [m for m in common_utils.get_available_metrics().keys() if m not in skip_metrics]
        metric_aggs = {k: v for k, v in common_utils.get_available_metrics().items() if k in metrics}
        metrics_db = await db_api.get_metrics_graph(
            workspace_id=ws.id, app_id=chat_id, type=metrics, type_groups=metric_aggs, object_base_type=int,
            start_date=start_date, end_date=end_date,
        )
        metrics_in_result = list(metrics_db.keys())

        outputs = await db_api.list_message_outputs(
            chat_id=chat_id, columns=['id', 'chat_message_id']
        )
    output_id_map = {o.id: o.chat_message_id for o in outputs}

    result = []
    for type, metric_list in metrics_db.items():
        item = {
            'type': type,
            'display_name': common_utils.to_display_name(type),
            'group': common_utils.metric_group(type),
            'points': [
                {
                    'created_at': p['updated_at'],
                    'timestamp': p['timestamp'],
                    'value': p['value'],
                    'object_id': p['object_id'],
                    # 'object_id': p['object_id'],
                    'item_id': output_id_map.get(p['object_id']),
                    'extra': p['extra'],
                }
                for p in metric_list
                if not existing_only or p['object_id'] in output_id_map
            ],
        }
        result.append(item)

    metric_names = common_utils.sorted_metric_names(list(metrics_in_result), common_utils.metric_views['inner'])

    return ORJSONResponse(content={'metrics': result, 'metric_names': metric_names}, status_code=200)


async def chat_doc_search(chat_id: int, q: str, limit=10):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        if ses.user_id != chat.owner_id and not all_chats_allowed:
            raise HTTPException(403, 'Forbidden')

        manager = await engine_manager_chats.ChatEngineManager.init_async(ctx.current_org(), ws, chat)
        result = await manager.search_in_docs(q, limit=limit)

    result_dicts = [r.dict() for r in result]
    return ORJSONResponse(content=result_dicts, status_code=200)


async def get_messages_metrics(
    chat_id: int,
    metrics: str = None,
    start_date: datetime.datetime = None,
    end_date: datetime.datetime = None,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        if ses.user_id != chat.owner_id and not all_chats_allowed:
            raise HTTPException(403, 'Forbidden')

        # skip_metrics = []
        if metrics:
            metrics = [m.strip() for m in metrics.split(',')]
            # skip_metrics = [m for m in common_utils.get_available_metrics().keys() if m not in metrics]

        outputs = await db_api.list_message_outputs(chat_id)
        # metric_dict = await common_utils.build_last_metrics(
        #     ws, models.APP_TYPE_CHAT, chat_id, skip_metrics,
        #     filter_by='object_id',
        #     filter_values=[o.id for o in outputs],
        #     filter_values_addition_ids=[o.chat_message_id for o in outputs],
        # )
        metric_dict = await common_utils.build_metrics_items_graph(
            ws, models.APP_TYPE_CHAT, chat_id, metrics,
            object_ids=[o.id for o in outputs],
            object_values_addition_ids=[o.chat_message_id for o in outputs],
            start_date=start_date,
            end_date=end_date,
        )

    return ORJSONResponse(content=metric_dict, status_code=200)


async def get_message_metrics(
    chat_id: int,
    message_id: int,
    result_id: int = None,
    metrics: str = None
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    async with base.session_context():
        message = await db_api.get_message_by_id(message_id)
        chat = await db_api.get_chat_by_id(chat_id)

        if message.chat_id != chat_id:
            raise HTTPException(404, f'Not found for id: {message_id}')

        all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
        if ses.user_id != chat.owner_id and not all_allowed:
            raise HTTPException(403, 'Must be the owner to get')

        skip_metrics = []
        if metrics:
            metrics = [m.strip() for m in metrics.split(',')]
            skip_metrics = [m for m in common_utils.get_available_metrics().keys() if m not in metrics]

        if result_id:
            output = await db_api.get_message_output_by_id(id=result_id)
            outputs = [output]
        else:
            outputs = await db_api.list_message_outputs(chat_id, chat_message_id=message_id)

        metric_dict = await common_utils.build_last_metrics(
            ws, models.APP_TYPE_CHAT, chat_id, filter_by='object_id',
            filter_values=[o.id for o in outputs],
            filter_values_addition_ids=[message_id] * len(outputs),
            skip_metrics=skip_metrics,
        )

        metrics = await db_api.get_last_metrics(workspace_id=ws.id, object_id=str(message_id))
        for m in metrics:
            m['display_name'] = common_utils.to_display_name(m['type'])

    return ORJSONResponse(content=metric_dict, status_code=200)


async def get_message(chat_id: int, message_id: int = None):
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        all_view = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
        if ses.user_id != chat.owner_id and not all_view:
            raise HTTPException(403, 'Forbidden')

        message = await db_api.get_message_by_id(message_id)
        if message.chat_id != chat.id:
            raise NotFoundException(f'Message not found for id: {message_id}')
        message_dict = message.to_dict()
        provided_answer = await db_api.get_provided_answer_by(app_id=chat_id, object_id=message_id)
        results = await db_api.list_message_outputs(chat_id, chat_message_id=message_id)

        suggestions = await db_api.list_suggestions(chat_id=chat_id, message_ids=[r.id for r in results])

    sug_map = collections.defaultdict(list)
    [sug_map[s.message_id].append(s.to_dict(skip_ids=True)) for s in suggestions]
    result_dicts = [r.to_dict(skip_ids=True) for r in results]
    for r in result_dicts:
        r['suggestions'] = sug_map[r['id']]

    # message_dict['suggestions'] = [s.to_dict() for s in suggestions]
    message_dict['results'] = result_dicts
    if provided_answer:
        message_dict['provided_answer'] = provided_answer.answer

    return ORJSONResponse(content=message_dict, status_code=200)


async def create_message_form(
    chat_id: int,
    content: str = Form(None),
    attachment: list[UploadFile] = Form(None),
):
    content = content or ''
    for at in (attachment or []):
        logger.info(f'Attached {at.filename}')
        if at.filename.lower().endswith('.json'):
            file_data = await at.read()
            try:
                # Wrap into markdown block
                content += '\n```json\n' + json.dumps(json.loads(file_data.decode()), indent=2) + '\n```'
            except Exception as e:
                raise HTTPException(400, f'Invalid json file {at.filename}: {e}')
        if at.filename.lower().endswith(('.txt', '.md', '.yaml', '.yml', '.py', '.ini')):
            file_data = await at.read()
            content += '\n\n' + file_data.decode()
    return await create_message(chat_id, ChatMessage(content=content or ''))


async def create_message(chat_id: int, message: ChatMessage):
    async with base.session_context() as db_session:
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != chat.owner_id and not all_chat_allowed:
            raise HTTPException(403, 'Must be the owner to write a message')

        message.content = message.content.replace('\r', '')
        new_message = {
            'content': message.content,
            'chat_id': chat_id,
            'owner_id': ses.user_id,
            'role': models.ROLE_USER,
            'status': models.STATUS_PROCESSING,
        }

        message = await db_api.create_message(new_message)
        # Need to flush session to get message.id
        await db_session.flush()
        output_message = await db_api.create_message_output({
            'chat_id': chat_id,
            'chat_message_id': message.id,
            'owner_id': ses.user_id,
            'role': models.ROLE_AI,
            'status': models.STATUS_PROCESSING,
        })

        ws = ctx.current_workspace()
        manager = await engine_manager_chats.ChatEngineManager.init_async(
            ctx.current_org(), ws=ws, app=chat
        )

    message_dict = message.to_dict()
    message_dict['results'] = [output_message.to_dict(skip_ids=True)]

    task = asyncio.create_task(manager.handle_message(chat, message, output_message))
    task2 = asyncio.create_task(metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE, obj_id=output_message.id,
    ))

    return ORJSONResponse(content=message_dict, status_code=200)


async def create_message_stream_form(
    chat_id: int,
    content: str = Form(None),
    attachment: list[UploadFile] = Form(None),
):
    content = content or ''
    for at in (attachment or []):
        logger.info(f'Attached {at.filename}')
        if at.filename.lower().endswith('.json'):
            file_data = await at.read()
            try:
                # Wrap into markdown block
                content += '\n```json\n' + json.dumps(json.loads(file_data.decode()), indent=2) + '\n```'
            except Exception as e:
                raise HTTPException(400, f'Invalid json file {at.filename}: {e}')
        if at.filename.lower().endswith(('.txt', '.md', '.yaml', '.yml', '.py', '.ini')):
            file_data = await at.read()
            content += '\n\n' + file_data.decode()
    return await create_message_stream(chat_id, ChatMessage(content=content))


async def create_message_stream(chat_id: int, message: ChatMessage):
    async with base.session_context() as db_session:
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != chat.owner_id and not all_chat_allowed:
            raise HTTPException(403, 'Must be the owner to write a message')

        new_message = {
            'content': message.content,
            'chat_id': chat_id,
            'owner_id': ses.user_id,
            'role': models.ROLE_USER,
            'status': models.STATUS_PROCESSING,
        }
        message = await db_api.create_message(new_message)
        await db_session.flush()
        output_message = await db_api.create_message_output({
            'chat_id': chat_id,
            'chat_message_id': message.id,
            'owner_id': ses.user_id,
            'role': models.ROLE_AI,
            'status': models.STATUS_PROCESSING,
        })

        ws = ctx.current_workspace()
        manager = await engine_manager_chats.ChatEngineManager.init_async(
            ctx.current_org(), ws=ws, app=chat
        )

    new_message = message.to_dict()
    new_message.update({'event_status': 'OK', 'event_type': 'message'})
    new_message['results'] = [output_message.to_dict(skip_ids=True)]

    await metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE, obj_id=output_message.id,
    )
    iterator_or_error = await manager.handle_message_stream(chat, message, output_message)
    logger.info('Done handle')

    return StreamingResponse(msg_streamer(
        iterator_or_error, new_message, output_message), media_type='application/x-ndjson'
    )


@jobs.job_tracker
async def msg_streamer(
    iterator: AsyncIterator | models.ChatMessageOutput, user_message: dict, output_message: models.ChatMessageOutput
):
    # Send the created message first
    chat_id = output_message.chat_id
    yield json.dumps(user_message) + '\n'
    # Then send received chunks
    if isinstance(iterator, models.ChatMessageOutput):
        # Received error message
        msg = iterator.to_dict()
        msg.update({'event_status': models.STATUS_ERROR, 'event_type': 'error'})
        yield json.dumps(msg) + '\n'
        return
    async for chunk in iterator:
        yield json.dumps(chunk) + '\n'

    # Wait for processing to complete with timeout
    start_time = asyncio.get_event_loop().time()
    while output_message.status == models.STATUS_PROCESSING:
        current_time = asyncio.get_event_loop().time()
        if current_time - start_time > MAX_PROCESSING_TIMEOUT:
            logger.error(f"Streaming timeout: Message {output_message.id} stuck in PROCESSING status for {MAX_PROCESSING_TIMEOUT}s")
            # Send timeout error to client
            timeout_dict = {
                'event_status': models.STATUS_ERROR,
                'event_type': 'timeout',
                'content': f'Processing timeout after {MAX_PROCESSING_TIMEOUT} seconds',
                'id': output_message.id
            }
            yield json.dumps(timeout_dict) + '\n'
            return

        output_message = await db_api.get_message_output_by_id(output_message.id)
        await asyncio.sleep(POLL_INTERVAL)

    # Status:
    # SUCCESS -> end
    # ERROR -> end
    # ERROR_REASON -> fetch 1 more message
    # GENERATE_QUESTION -> wait for next status

    output_dict = output_message.to_dict()
    output_dict.update({'event_status': 'OK', 'event_type': 'update'})
    yield json.dumps(output_dict) + '\n'

    # Wait for status change or new message error
    old_status = output_message.status
    if old_status == models.STATUS_ERROR_REASON or old_status == models.STATUS_ERROR:
        return
    if old_status == models.STATUS_SUCCESS:
        suggestions = await db_api.list_suggestions(chat_id=chat_id, message_id=output_message.id)
        output_dict = output_message.to_dict()
        output_dict['suggestions'] = [s.to_dict() for s in suggestions]
        output_dict.update({'event_status': 'OK', 'event_type': 'update'})
        yield json.dumps(output_dict) + '\n'

    # wait for success/error with timeout
    status_wait_start = asyncio.get_event_loop().time()
    while output_message.status not in [models.STATUS_ERROR, models.STATUS_ERROR_REASON, models.STATUS_SUCCESS]:
        current_time = asyncio.get_event_loop().time()
        if current_time - status_wait_start > MAX_STATUS_WAIT_TIMEOUT:
            logger.error(f"Streaming timeout: Message {output_message.id} stuck waiting for final status for {MAX_STATUS_WAIT_TIMEOUT}s")
            # Send timeout error to client
            timeout_dict = {
                'event_status': models.STATUS_ERROR,
                'event_type': 'status_timeout',
                'content': f'Status wait timeout after {MAX_STATUS_WAIT_TIMEOUT} seconds',
                'id': output_message.id
            }
            yield json.dumps(timeout_dict) + '\n'
            return

        output_message = await db_api.get_message_output_by_id(output_message.id)
        await asyncio.sleep(POLL_INTERVAL)

    new_status = output_message.status
    if new_status == models.STATUS_SUCCESS:
        suggestions = await db_api.list_suggestions(chat_id=chat_id, message_id=output_message.id)
        output_dict = output_message.to_dict()
        output_dict['suggestions'] = [s.to_dict() for s in suggestions]
        output_dict.update({'event_status': 'OK', 'event_type': 'update'})
        yield json.dumps(output_dict) + '\n'
        return
    elif new_status != old_status:
        output_dict = output_message.to_dict()
        output_dict.update({'event_status': 'OK', 'event_type': 'update'})
        yield json.dumps(output_dict) + '\n'

        answer_msgs = await db_api.list_messages(chat_id=chat_id, start_id=user_message['id'], limit=3)
        if answer_msgs:
            output_dict = answer_msgs[-1].to_dict()
            output_dict.update({'event_status': 'OK', 'event_type': 'update'})
            yield json.dumps(output_dict) + '\n'
        return


async def edit_message(chat_id: int, message_id: int, message: EditMessage):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to edit')

    # messages_after = await db_api.list_messages(chat_id, start_id=message_id, limit=1)
    # if len(messages_after) > 0:
    #     raise HTTPException(400, 'Must be the last message')

    updates = message.model_dump(exclude_unset=True)
    if not updates:
        raise HTTPException(400, 'Provide data to update')

    message = await db_api.update_message_by_id(message_id, updates)
    message_dict = message.to_dict()
    results = await db_api.list_message_outputs(chat_id, chat_message_id=message_id)
    suggestions = await db_api.list_suggestions(chat_id=chat_id, message_ids=[r.id for r in results])
    sug_map = collections.defaultdict(list)
    [sug_map[s.message_id].append(s.to_dict(skip_ids=True)) for s in suggestions]
    result_dicts = [r.to_dict(skip_ids=True) for r in results]
    for r in result_dicts:
        r['suggestions'] = sug_map[r['id']]

    message_dict['results'] = result_dicts
    return ORJSONResponse(content=message_dict, status_code=200)


async def change_message(chat_id: int, message_id: int, change: MessageChange):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to edit')

    if change.rating and (change.rating > 10 or change.rating < -1):
        raise HTTPException(400, 'Message rating should be in range [-1, 10]')

    if change.provided_answer:
        if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.FILE_MANAGE):
            raise HTTPException(403, 'Must have file.manage permission to provide answer')

        # Compose & re-index file
        question = await db_api.get_message_by_id(message_id)
        if not question:
            raise HTTPException(422, 'Question message is not available')
        provided_answer = await db_api.get_provided_answer_by(workspace_id=ctx.current_workspace().id, app_id=chat_id, object_id=message_id)
        if provided_answer:
            provided_answer = await db_api.update_provided_answer(provided_answer, {'answer': change.provided_answer})
        else:
            provided_answer = await db_api.create_provided_answer({
                'workspace_id': ctx.current_workspace().id,
                'app_id': chat_id,
                'object_id': message_id,
                'answer': change.provided_answer,
                'question': question.content,
            })

        manager = await engine_manager_chats.ChatEngineManager.init_async(
            ctx.current_org(), ws=ctx.current_workspace(), app=chat
        )
        last_result = await db_api.get_last_message_output(
            chat_id, message_id=message_id, workspace_id=ctx.current_workspace().id
        )
        last_result_id = last_result.id if last_result else change.result_id

        await index_provided_answers(manager, [provided_answer], caller_id=ses.user_id)
        await metric_utils.ensure_metric(
            ctx.current_org().id, ctx.current_workspace().id, app_id=chat_id, result_id=last_result_id, type_=models.METRIC_CORRECTED_ANSWER,
            value=1, set_date=last_result.updated_at,
        )
        # asyncio.create_task(index_provided_answers(manager, caller_id=ses.user_id))

    change_dict = change.model_dump(exclude_unset=True)
    change_dict.pop('provided_answer', None)
    change_dict.pop('result_id', None)
    tags = change_dict.pop('tags', False)

    if change.result_id:
        result = await db_api.get_message_output_by_id(change.result_id)
    else:
        result = await db_api.get_last_message_output(
            chat_id, message_id=message_id, workspace_id=ctx.current_workspace().id
        )

    if change_dict:
        if not change.result_id:
            raise HTTPException(400, 'Provide result_id parameter to change')
        await db_api.update_message_output_by_id(change.result_id, change_dict)

    message = await db_api.get_message_by_id(message_id)

    if tags is not False and tags is not None and isinstance(tags, list):
        await db_api.update_message(message, {'tags': tags})
        # Update appropriate metrics
        if tags:
            await db_api.update_metrics(
                {'tag': tags[0]},
                workspace_id=ctx.current_workspace().id, app_id=chat_id,
                object_id=result.id, app_type=models.APP_TYPE_CHAT
            )

    message_dict = message.to_dict()
    if change.provided_answer:
        message_dict['provided_answer'] = provided_answer.answer
    if change.rating:
        # Add metric rating
        await metric_utils.ensure_rating_metric(
            ctx.current_org().id, ctx.current_workspace().id, chat_id, change.result_id,
            change.rating, set_date=result.updated_at, tag=message.tags[0] if message.tags else None
        )
    if change.grade_selector:
        # Add metric correctness grade
        await metric_utils.ensure_grade_metric(
            ctx.current_org().id, ctx.current_workspace().id, chat_id, change.result_id,
            change.grade_selector, set_date=result.updated_at, tag=message.tags[0] if message.tags else None
        )

    results = await db_api.list_message_outputs(chat_id, chat_message_id=message_id)
    message_dict['results'] = [r.to_dict(skip_ids=True) for r in results]
    return ORJSONResponse(content=message_dict, status_code=200)


@jobs.job_tracker
async def index_provided_answers(
    manager: engine_manager_chats.ChatEngineManager,
    new_answers: List[models.ProvidedAnswer],
    caller_id=None
):
    return await index_provided_answer_dataset(manager, new_answers, caller_id)


async def index_provided_answer_dataset(
    manager: engine_manager_chats.ChatEngineManager,
    new_answers: List[models.ProvidedAnswer],
    caller_id=None
):
    # Logic: append the new answer to already existing file
    org = manager.org

    file_manager = SharedConfig().file_manager

    new_answer_data = ''
    for new_answer in new_answers:
        new_answer_data += (
            f"Human: {new_answer.question}\n\n\n"
            f"AI: {new_answer.answer}\n"
        )

    dataset = manager.get_working_dataset()
    filename = utils.get_provided_answer_filename(dataset)
    ds_manager = dataset_manager.DatasetManager(org, dataset)
    file = await db_api.get_dataset_file_by_ds_and_name(dataset_id=dataset.id, name=filename)
    if not file:
        new_answer_data = new_answer_data.encode()
        file = await db_api.create_dataset_file({
            'owner_id': caller_id,
            'name': filename,
            'size': len(new_answer_data),
            'dataset_id': dataset.id,
            'org_id': org.id,
            'hash': utils.hash_sha256(new_answer_data),
            'status': {'status': models.STATUS_PROCESSING, 'message': None},
            'source_type': models.SourceType.CORRECTED_TXT,
        })
        async with base.session_context():
            await db_api.update_dataset(dataset, {
                'total_size': await db_api.get_dataset_file_size_sum(dataset.id),
                'file_count': await db_api.get_dataset_file_count(dataset_id=dataset.id),
            })
    else:
        # Read previously saved file
        storage_name = get_storage_dataset_file_name(org.name, dataset.name, file)
        file_data = await file_manager.read_and_get_data(storage_name)

        # Append data to it
        new_answer_data = f'{file_data.decode()}\n{new_answer_data}'
        new_answer_data = new_answer_data.encode()

        # Delete the previous data
        file = await db_api.update_dataset_file(
            file,
            {
                'status': {'status': models.STATUS_PROCESSING, 'message': None},
                'size': len(new_answer_data),
                'hash': utils.hash_sha256(new_answer_data),
            }
        )
        await ds_manager.handle_delete_file(file)
        await file_manager.delete_file(storage_name)

    # Process new data
    storage_name = get_storage_dataset_file_name(org.name, dataset.name, file)
    await file_manager.save_file(storage_name, new_answer_data)
    coro1 = asyncio.create_task(ds_manager.process_file_for_all_indexes(file, reprocess=True))


async def delete_message(chat_id: int, message_id: int, result_id: Optional[int] = None):
    async with base.session_context():
        chat = await db_api.get_chat_by_id(chat_id)

        ses = ctx.current_session()
        ws = ctx.current_workspace()
        all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
        if ses.user_id != chat.owner_id and not all_chat_allowed:
            raise HTTPException(403, 'Must be the owner to delete')

        # message = await db_api.get_message_by_id(message_id)
        # messages_after = await db_api.list_messages(chat_id, start_id=message_id, limit=1)
        # if len(messages_after) > 0:
        #     raise HTTPException(400, 'Must be the last message')

        results = await db_api.list_message_outputs(chat_id, chat_message_id=message_id)

        if result_id:
            await db_api.delete_suggestions(chat_id=chat_id, message_ids=[result_id])
            await db_api.delete_message_output(id=result_id)
        else:
            await db_api.delete_suggestions(chat_id=chat_id, message_ids=[r.id for r in results])
            await db_api.delete_message(message_id)
            await db_api.delete_message_outputs(chat_id=chat_id, chat_message_id=message_id)

        await db_api.update_metrics_for_chat(chat)
        await metric_utils.create_metric(ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE_DELETED, value=1)

    return Response(status_code=204)


async def delete_messages(chat_id: int, last: int = 1):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to delete')

    async with base.session_context():
        last_messages = await db_api.list_messages(chat_id, order_by='id', desc=True, limit=last or 1)
        results = await db_api.list_message_outputs(chat_id, chat_message_ids=[m.id for m in last_messages])

        for last_message in last_messages:
            await db_api.delete_message(last_message.id)
        await db_api.delete_suggestions_for_messages(chat_id=chat_id, message_ids=[r.id for r in results])
        await db_api.delete_message_outputs(chat_id=chat_id, chat_message_ids=[m.id for m in last_messages])

        await metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE_DELETED, value=chat.metrics.get('message_count', 0)
        )

        await db_api.update_metrics_for_chat(chat)

    return Response(status_code=204)


async def rollback_message(chat_id: int):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to rollback')

    last_messages = await db_api.list_messages(chat_id, order_by='id', desc=True, limit=5)

    deleted = []
    for last_message in last_messages:
        await db_api.delete_message(last_message.id)
        deleted.append(last_message.to_dict())
        if last_message.role == models.ROLE_USER:
            break

    results = await db_api.list_message_outputs(chat_id, chat_message_ids=[m['id'] for m in deleted])
    await db_api.delete_suggestions_for_messages(chat_id=chat_id, message_ids=[r.id for r in results])
    await db_api.update_metrics_for_chat(chat)
    await metric_utils.create_metric(
        ctx.current_org().id, workspace_id=ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE_DELETED, value=len(deleted)
    )

    return ORJSONResponse(status_code=200, content={'deleted_messages': deleted})


async def regenerate_message(chat_id: int):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to regenerate')

    last_messages = await db_api.list_messages(chat_id, order_by='id', desc=True, limit=5)

    deleted = []
    user_message = None
    for last_message in last_messages:
        if last_message.role == models.ROLE_USER:
            user_message = await db_api.update_message(last_message, {'status': models.STATUS_PROCESSING})
            break
        await db_api.delete_message(last_message.id)
        deleted.append(last_message.to_dict())

    output_message = None
    if user_message:
        output_message = await db_api.create_message_output({
            'chat_id': chat_id,
            'chat_message_id': user_message.id,
            'owner_id': ses.user_id,
            'role': models.ROLE_AI,
            'status': models.STATUS_PROCESSING,
        })

        ws = ctx.current_workspace()
        manager = await engine_manager_chats.ChatEngineManager.init_async(
            ctx.current_org(), ws=ctx.current_workspace(), app=chat
        )
        task = asyncio.create_task(manager.handle_message(chat, user_message, output_message))
        await metric_utils.create_metric(
            ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE, obj_id=output_message.id,
        )

    return ORJSONResponse(status_code=200, content={
        'deleted_messages': deleted,
        'user_message': user_message.to_dict() if user_message else None,
        'new_message': output_message.to_dict() if output_message else None,
    })


async def regenerate_message_stream(chat_id: int):
    chat = await db_api.get_chat_by_id(chat_id)

    ses = ctx.current_session()
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != chat.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to regenerate')

    last_messages = await db_api.list_messages(chat_id, order_by='id', desc=True, limit=5)

    deleted = []
    user_message = None
    for last_message in last_messages:
        if last_message.role == models.ROLE_USER:
            user_message = await db_api.update_message(last_message, {'status': models.STATUS_PROCESSING})
            break
        await db_api.delete_message(last_message.id)
        deleted.append(last_message.to_dict())

    if not user_message:
        raise HTTPException(400, "Nothing to regenerate")

    old_outputs = await db_api.list_message_outputs(chat_id=chat_id, chat_message_id=user_message.id)
    output_message = await db_api.create_message_output({
        'chat_id': chat_id,
        'chat_message_id': user_message.id,
        'owner_id': ses.user_id,
        'role': models.ROLE_AI,
        'status': models.STATUS_PROCESSING,
    })

    ws = ctx.current_workspace()
    manager = await engine_manager_chats.ChatEngineManager.init_async(
        ctx.current_org(), ws=ctx.current_workspace(), app=chat
    )
    iterator_or_error = await manager.handle_message_stream(chat, user_message, output_message)
    await metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=chat_id, type_=models.METRIC_MESSAGE, obj_id=output_message.id,
    )

    old_output_dicts = [o.to_dict(skip_ids=True) for o in old_outputs]
    new_message = user_message.to_dict()
    new_message.update({'event_status': 'OK', 'event_type': 'update'})
    new_message['results'] = old_output_dicts + [output_message.to_dict(skip_ids=True)]

    return StreamingResponse(msg_streamer(
        iterator_or_error, new_message, output_message), media_type='application/x-ndjson'
    )
