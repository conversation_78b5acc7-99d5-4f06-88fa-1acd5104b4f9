from sqlalchemy import and_, or_
from sqlalchemy import update
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def list_provided_answers(workspace_id: int, app_id: int = None, object_ids: list[int] = None, session=None):
    q = select(models.ProvidedAnswer).where(models.ProvidedAnswer.workspace_id == workspace_id)
    if app_id:
        q = q.where(models.ProvidedAnswer.app_id == app_id)
    if object_ids:
        q = q.where(models.ProvidedAnswer.object_id.in_(object_ids))
    q = q.order_by(models.ProvidedAnswer.id)

    res = await session.execute(q)

    return res.scalars().fetchall()


@base.session_aware()
async def get_provided_answer_by(
    id: int = None,
    workspace_id: int = None,
    app_id: int = None,
    object_id: int = None,
    session=None
):
    q = select(models.ProvidedAnswer)
    if id:
        q = q.where(models.ProvidedAnswer.id == id)
    if workspace_id:
        q = q.where(models.ProvidedAnswer.workspace_id == workspace_id)
    if app_id:
        q = q.where(models.ProvidedAnswer.app_id == app_id)
    if object_id:
        q = q.where(models.ProvidedAnswer.object_id == object_id)
    res = await session.execute(q)
    res = res.scalar()

    return res


@base.session_aware()
async def create_provided_answer(values, session=None):
    return await base.create_model(models.ProvidedAnswer, values, session=session)


@base.session_aware()
async def update_provided_answer(answer, new_values, session=None):
    # new_values['updated_at'] = models.now()
    update_q = update(models.ProvidedAnswer).where(models.ProvidedAnswer.id == answer.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    answer.update(new_values)

    return answer


@base.session_aware()
async def bulk_get_provided_answers(lookups: list[dict], session=None):
    """
    Bulk get provided answers by workspace_id, app_id, object_id.
    lookups: list of dicts with 'workspace_id', 'app_id', 'object_id'
    Returns: dict mapping (workspace_id, app_id, object_id) -> ProvidedAnswer
    """
    if not lookups:
        return {}

    # Build OR conditions for all lookups

    conditions = []
    for lookup in lookups:
        condition = and_(
            models.ProvidedAnswer.workspace_id == lookup['workspace_id'],
            models.ProvidedAnswer.app_id == lookup['app_id'],
            models.ProvidedAnswer.object_id == lookup['object_id']
        )
        conditions.append(condition)

    q = select(models.ProvidedAnswer).where(or_(*conditions))
    res = await session.execute(q)
    answers = res.scalars().fetchall()

    # Create lookup map
    result = {}
    for answer in answers:
        key = (answer.workspace_id, answer.app_id, answer.object_id)
        result[key] = answer

    return result


@base.session_aware()
async def bulk_create_provided_answers(values_list: list[dict], session=None):
    """
    Bulk create provided answers.
    values_list: list of dicts with provided answer data
    """
    if not values_list:
        return []

    answers = [models.ProvidedAnswer(**values) for values in values_list]
    session.add_all(answers)
    return answers


@base.session_aware()
async def bulk_update_provided_answers(updates: list[dict], session=None):
    """
    Bulk update provided answers.
    updates: list of dicts with 'id' and update fields
    """
    if not updates:
        return

    from sqlalchemy import case

    # Extract all unique fields being updated
    all_fields = set()
    for update_item in updates:
        all_fields.update(k for k in update_item.keys() if k != 'id')

    # Build case statements for each field
    update_values = {}
    for field in all_fields:
        cases = []
        for update_item in updates:
            if field in update_item:
                cases.append((models.ProvidedAnswer.id == update_item['id'], update_item[field]))

        if cases:
            update_values[field] = case(*cases, else_=getattr(models.ProvidedAnswer, field))

    if update_values:
        update_q = update(models.ProvidedAnswer).where(
            models.ProvidedAnswer.id.in_([u['id'] for u in updates])
        ).values(update_values)

        await session.execute(update_q)
