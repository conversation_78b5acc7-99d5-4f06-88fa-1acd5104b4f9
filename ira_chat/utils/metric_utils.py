import datetime
import math

from ira_chat.api.common_utils import get_available_metrics
from ira_chat.db import models
from ira_chat.db import api as db_api

grade_map = {
    models.GradeSelector.CORRECT_ANSWER: 1,
    models.GradeSelector.INCORRECT_ANSWER: 0,
    models.GradeSelector.PARTIAL_ANSWER: 0,
    models.GradeSelector.NO_ANSWER_EXCLUDED: None,
    models.GradeSelector.NO_ANSWER_COMPLETELY: None,
    models.GradeSelector.OFF_TOPIC_NO_ANSWER: None,
}


async def create_metric(
    org_id,
    workspace_id,
    app_id,
    type_,
    obj_id=None,
    app_type=models.APP_TYPE_CHAT,
    value: float = 1,
    set_date: datetime.datetime = None,
    tag: str = None,
):
    # if value == 0:
    #     return
    metric = {
        'org_id': org_id,
        'workspace_id': workspace_id,
        'app_id': app_id,
        'app_type': app_type,
        'object_id': obj_id,
        'type': type_,
        'value': value,
        'tag': tag,
    }
    if set_date:
        metric['created_at'] = set_date
        metric['updated_at'] = set_date

    await db_api.create_metric(metric)


def grade_to_value(grade: str):
    return grade_map.get(grade)


def grade_to_event_name(grade: str):
    # 'Excluded answer' -> 'excluded_answer'
    # 'No answer' -> 'no_answer'
    # 'Off-topic no answer' -> 'off_topic_no_answer'
    return grade.lower().replace(' ', '_')


def grade_to_rating(grade: str):
    value = grade_to_value(grade)
    # 0 -> 1
    # 0.5 -> 3
    # 1 -> 5
    # None -> None
    if value is None:
        return None
    rating = max(int(math.ceil(value * 5)), 1)
    return rating


async def ensure_metric(
    org_id, workspace_id, app_id, result_id, type_: str, value: float,
    app_type=models.APP_TYPE_CHAT,
    set_date: datetime.datetime = None,
    tag: str = None,
):
    existing_value = await db_api.get_metric_one(
        workspace_id, type_, app_type, app_id, result_id
    )

    if existing_value:
        if existing_value.value == value and existing_value.tag == tag:
            return
        await db_api.update_metric(existing_value, {'value': value, 'updated_at': set_date, 'tag': tag})
    else:
        await create_metric(
            org_id, workspace_id, app_id=app_id, type_=type_,
            obj_id=result_id, app_type=app_type, value=value, set_date=set_date, tag=tag
        )


async def delete_unneeded_metric(workspace_id, app_id, result_id, type_: str | list[str], app_type=models.APP_TYPE_CHAT):
    await db_api.delete_metrics_by(
        workspace_id=workspace_id,
        app_type=app_type,
        app_id=app_id,
        object_id=result_id,
        type=type_
    )


async def ensure_grade_metric(
    org_id, workspace_id, chat_id, result_id, grade_value: str, set_date: datetime.datetime = None, tag: str = None
):
    grade_metric = grade_to_value(grade_value)

    if grade_metric is not None:
        await ensure_metric(
            org_id, workspace_id, chat_id, result_id, models.METRIC_CORRECTNESS_GRADE, grade_metric, set_date=set_date, tag=tag
        )
        # Set generic grade event only for selected grades
        await ensure_metric(
            org_id, workspace_id, chat_id, result_id, models.METRIC_GRADE_EVENT, 1, set_date=set_date, tag=tag
        )
    else:
        # Delete generic grade event for grades that are not selected
        await delete_unneeded_metric(workspace_id, chat_id, result_id, models.METRIC_GRADE_EVENT, app_type=models.APP_TYPE_CHAT)

    # Set named grade event
    await ensure_metric(
        org_id, workspace_id, chat_id, result_id, grade_to_event_name(grade_value), 1, set_date=set_date, tag=tag
    )
    # Ensure named grade event is only one
    types = []
    for grade in grade_map.keys():
        if grade != grade_value:
            types.append(grade_to_event_name(grade))
    await delete_unneeded_metric(workspace_id, chat_id, result_id, types, app_type=models.APP_TYPE_CHAT)


async def ensure_rating_metric(
    org_id, workspace_id, chat_id, result_id, rating_value: int, set_date: datetime.datetime = None, tag: str = None
):
    if rating_value is None:
        return
    await ensure_metric(
        org_id, workspace_id, chat_id, result_id, models.METRIC_RATING_VALUE, rating_value, set_date=set_date
    )
    await ensure_metric(
        org_id, workspace_id, chat_id, result_id, models.METRIC_RATING_EVENT, 1, set_date=set_date
    )


async def squash_metrics(
    org_id, workspace_id, app_id, app_type, object_id,
    pick_metric=models.METRIC_RESPONSE_TIME,
    set_date: datetime.datetime = None
):
    # Get aggregated metrics
    metric_aggs = {k: v for k, v in get_available_metrics().items()}
    # Get any metric to guess a tag
    metric_one = await db_api.get_metric_one(
        workspace_id, metric_type=pick_metric, app_id=app_id, app_type=app_type, object_id=object_id,
    )
    aggregated_metrics = await db_api.get_metrics(
        [workspace_id], app_type=app_type, app_id=app_id, object_id=object_id,
        type_groups=metric_aggs, agg_groups=['object_id', 'extra']
    )
    await db_api.delete_metrics_by(workspace_id, app_type, app_id, object_id)
    for metric in aggregated_metrics:
        metric['object_id'] = object_id
        if metric_one:
            metric['tag'] = metric_one.tag
        await db_api.create_metric({
            'org_id': org_id,
            'workspace_id': workspace_id,
            'app_id': app_id,
            'app_type': app_type,
            'object_id': object_id,
            'type': metric['type'],
            'value': metric['value'],
            'tag': metric.get('tag'),
            'created_at': set_date if set_date else metric_one.created_at,
            'updated_at': set_date if set_date else metric_one.updated_at,
            'extra': metric.get('extra'),
        })
