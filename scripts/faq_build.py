import argparse
import asyncio
import json
import logging
import os
from collections import Counter
from typing import List, Dict, Any, Tuple

import hdbscan
import numpy as np
import pandas as pd
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.documents import Document
from langchain_core.prompts import PromptTemplate
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from pydantic import BaseModel
from sklearn.cluster import AgglomerativeClustering, KMeans
from sklearn.metrics.pairwise import cosine_distances


def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    # Set debug level if DEBUG env var is set
    if os.getenv('DEBUG') == 'true':
        logging.getLogger().setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)


def load_data(path: str) -> pd.DataFrame:
    """
    Load FAQ data from CSV or other tabular format.
    Expected columns: 'question', 'answer', 'created_at', 'rating', 'answer_relevancy'
    """
    logger.info(f"Loading data from {path}")

    # Use more robust CSV parsing options to handle complex text with quotes and newlines
    try:
        # First attempt with standard options
        df = pd.read_csv(path, parse_dates=['created_at'])
    except Exception as e:
        logger.warning(f"Standard CSV parsing failed: {e}")
        logger.info("Trying with more robust parsing options...")

        # Try with more robust options for complex CSV files
        df = pd.read_csv(
            path,
            parse_dates=['created_at'],
            quoting=1,  # QUOTE_ALL
            escapechar='\\',
            on_bad_lines='warn'
        )

    logger.info(f"Loaded {len(df)} records")

    # Validate required columns
    required_columns = ['question', 'answer']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    # Check for NaN values in required columns
    for col in required_columns:
        nan_count = df[col].isna().sum()
        if nan_count > 0:
            logger.warning(f"Found {nan_count} NaN values in required column '{col}'")
            # filter out NaN values
            df = df.dropna(subset=[col])

    # Log optional columns status
    optional_columns = ['created_at', 'rating', 'answer_relevancy']
    for col in optional_columns:
        if col not in df.columns:
            logger.warning(f"Optional column '{col}' not found in data")

    # Extract topic and topic_group from context
    df['context'] = df['context'].apply(json.loads)
    df['topic_group'] = df['context'].apply(lambda x: x.get('topic_group', 'Unknown'))
    df['topic'] = df['context'].apply(lambda x: x.get('topic', 'Unknown'))

    return df


def compute_embeddings(questions: List[str]) -> np.ndarray:
    """
    Compute OpenAI embeddings for a list of questions.
    Expects OPENAI_API_KEY to be set in environment.
    """
    logger.info(f"Computing embeddings for {len(questions)} questions")

    # Filter out non-string values and convert any remaining non-string values to strings
    valid_questions = []
    for q in questions:
        if pd.isna(q):
            logger.warning(f"Skipping NaN question")
            continue
        if not isinstance(q, str):
            logger.warning(f"Converting non-string question to string: {type(q)}")
            q = str(q)
        valid_questions.append(q)

    if len(valid_questions) < len(questions):
        logger.warning(f"Filtered out {len(questions) - len(valid_questions)} invalid questions")

    if not valid_questions:
        logger.error("No valid questions to embed")
        return np.array([])

    embedder = OpenAIEmbeddings()
    embeddings = embedder.embed_documents(valid_questions)
    logger.info(f"Successfully computed embeddings with shape: {np.array(embeddings).shape}")
    return np.array(embeddings)


def cluster_embeddings(
    embeddings: np.ndarray,
    method: str = 'hdbscan',
    expected_clusters: int = None,
    **kwargs
) -> np.ndarray:
    """
    Cluster embeddings using specified method with optimized defaults for FAQ data.

    Args:
        embeddings: Embedding vectors to cluster (should be normalized, like OpenAI embeddings)
        method: 'hdbscan', 'agglomerative', or 'kmeans'
        expected_clusters: Expected number of clusters (helps set better defaults)
        **kwargs: Additional parameters for the clustering algorithm

    Note:
        For HDBSCAN, cosine distance is always used (optimal for OpenAI embeddings).
        The metric parameter is ignored for HDBSCAN as it uses precomputed cosine distances.
    """
    n_samples = len(embeddings)
    logger.info(f"Clustering {n_samples} embeddings using {method}")

    # Estimate expected clusters if not provided
    if expected_clusters is None:
        # For FAQ data, assume 5-10 questions per cluster on average
        expected_clusters = max(10, n_samples // 7)

    logger.info(f"Expected clusters: {expected_clusters}")

    if method == 'hdbscan':
        # Optimized defaults for FAQ clustering with cosine distance
        if 'min_cluster_size' not in kwargs:
            # For ~1000 samples expecting ~150 clusters: min_cluster_size should be small
            kwargs['min_cluster_size'] = max(2, min(5, n_samples // (expected_clusters * 2)))
        if 'min_samples' not in kwargs:
            kwargs['min_samples'] = max(1, kwargs['min_cluster_size'] // 2)
        if 'cluster_selection_epsilon' not in kwargs:
            # Help merge very close clusters
            kwargs['cluster_selection_epsilon'] = 0.1

        # Force cosine distance for OpenAI embeddings by using precomputed distance matrix
        logger.info("Computing cosine distance matrix for HDBSCAN (optimal for OpenAI embeddings)")
        cosine_dist_matrix = cosine_distances(embeddings)

        # Set metric to precomputed and remove any user-specified metric
        kwargs['metric'] = 'precomputed'

        logger.info(f"HDBSCAN parameters: {kwargs}")
        logger.info(f"Using cosine distance matrix of shape: {cosine_dist_matrix.shape}")

        clusterer = hdbscan.HDBSCAN(**kwargs)
        labels = clusterer.fit_predict(cosine_dist_matrix)

    elif method == 'agglomerative':
        # Optimized defaults for FAQ clustering
        if 'n_clusters' not in kwargs:
            kwargs['n_clusters'] = expected_clusters
        if 'metric' not in kwargs:
            # For AgglomerativeClustering, cosine is supported
            kwargs['metric'] = 'cosine'
        if 'linkage' not in kwargs:
            # Average linkage works better with cosine distance
            kwargs['linkage'] = 'average'

        logger.info(f"AgglomerativeClustering parameters: {kwargs}")
        clusterer = AgglomerativeClustering(**kwargs)
        labels = clusterer.fit_predict(embeddings)

    elif method == 'kmeans':
        # K-means as an alternative option
        if 'n_clusters' not in kwargs:
            kwargs['n_clusters'] = expected_clusters
        if 'n_init' not in kwargs:
            kwargs['n_init'] = 10

        logger.info(f"KMeans parameters: {kwargs}")
        clusterer = KMeans(**kwargs)
        labels = clusterer.fit_predict(embeddings)

    else:
        raise ValueError(f"Unknown clustering method: {method}. Supported: 'hdbscan', 'agglomerative', 'kmeans'")

    # Log clustering results with quality metrics
    unique_labels = np.unique(labels)
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)  # Exclude noise
    n_noise = np.sum(labels == -1) if -1 in unique_labels else 0

    # Calculate cluster size statistics
    if n_clusters > 0:
        cluster_sizes = [np.sum(labels == label) for label in unique_labels if label != -1]
        avg_cluster_size = np.mean(cluster_sizes)
        min_cluster_size = np.min(cluster_sizes)
        max_cluster_size = np.max(cluster_sizes)

        logger.info(f"Clustering results: {n_clusters} clusters, {n_noise} noise points")
        logger.info(f"Cluster size stats - avg: {avg_cluster_size:.1f}, min: {min_cluster_size}, max: {max_cluster_size}")

        # Quality warnings
        if n_clusters < expected_clusters * 0.5:
            logger.warning(f"Too few clusters ({n_clusters}) compared to expected ({expected_clusters}). Consider reducing min_cluster_size or cluster_selection_epsilon.")
        elif n_clusters > expected_clusters * 2:
            logger.warning(f"Too many clusters ({n_clusters}) compared to expected ({expected_clusters}). Consider increasing min_cluster_size or cluster_selection_epsilon.")
    else:
        logger.warning("No clusters found! All points classified as noise.")

    return labels


async def summarize_cluster(
    cluster_id: int,
    questions: List[str],
    answers: List[str],
    categories: List[str],
    subcategories: List[str],
    llm_model: str = 'gpt-4.1',
    small_llm_model: str = 'gpt-4o-mini',
    temperature: float = 0.5,
    semaphore: asyncio.Semaphore = None,
    language: str = 'French',
) -> Dict[str, Any]:
    """
    Async version of summarize_cluster for concurrent processing.
    Uses semaphore to limit concurrent LLM calls.

    Generates FAQ-style canonical questions and consolidated answers.
    Merges a list of source categories and subcategories into a single category.
    For answer consolidation, includes the canonical question in the prompt
    to ensure the answer directly addresses the FAQ question.

    The generated answers are formatted specifically for FAQ use, excluding
    chatbot-style elements like "feel free to ask", summary sections, and
    conversational language.

    Returns dict with cluster_id, canonical_question, consolidated_answer, source_count
    """
    async with semaphore:
        logger.info(f"Summarizing cluster {cluster_id} with {len(questions)} questions and {len(answers)} answers")

        # Handle edge cases
        if not questions or not answers:
            logger.warning(f"Empty questions or answers list provided for cluster {cluster_id}")
            return {
                'cluster_id': cluster_id,
                'canonical_question': "",
                'consolidated_answer': "",
                'category': "",
                'subcategory': "",
                'source_count': 0,
                'questions': questions,
                'answers': answers,
                'source_categories': categories,
                'source_subcategories': categories
            }

        if len(questions) == 1 and len(answers) == 1:
            logger.info(f"Single question/answer pair for cluster {cluster_id}, applying FAQ formatting")

            # Initialize LLM for single item formatting
            llm = ChatOpenAI(model=llm_model, temperature=temperature)

            # Format single question as FAQ question
            question_prompt = PromptTemplate.from_template(
                "You are creating a FAQ (Frequently Asked Questions) entry. Rewrite this user question to be a clear, professional FAQ question that would be appropriate for a FAQ section.\n\n"
                "The question should:\n"
                "- Be professionally worded and clear\n"
                "- Be phrased as a typical FAQ question that users would expect to find\n"
                "- Maintain the original intent and meaning\n"
                f"- Be in {language} language\n\n"
                "User question: {question}\n\n"
                "Generate a professional FAQ question:"
            )

            canonical_q = await llm.ainvoke(question_prompt.format(question=questions[0]))

            # Format single answer as FAQ answer
            answer_prompt = PromptTemplate.from_template(
                "You are creating a FAQ (Frequently Asked Questions) entry. Rewrite this answer to be a comprehensive, professional FAQ answer for the following question.\n\n"
                "FAQ Question: {canonical_question}\n\n"
                "Your answer should:\n"
                "- Directly address the FAQ question above\n"
                "- Be written in a professional, informational FAQ style\n"
                "- Be clear, concise, and actionable\n"
                "- Be structured and easy to read\n"
                "- Focus on providing information, not conversation\n\n"
                f"- Be in {language} language\n\n"
                "IMPORTANT: Do NOT include:\n"
                "- Summary sections\n"
                "- Phrases like 'feel free to ask', 'don't hesitate to contact', 'further assistance'\n"
                "- Conversational elements or chatbot-style language\n"
                "- Invitations for follow-up questions\n"
                "- Any concluding statements about getting help\n\n"
                "Original answer: {answer}\n\n"
                "Generate a direct, informational FAQ answer:"
            )

            consolidated_a = await llm.ainvoke(answer_prompt.format(
                canonical_question=canonical_q.content.strip(),
                answer=answers[0]
            ))

            merged_category, merged_subcategory = await summarize_categories(
                questions[:5] if len(questions) > 5 else questions,
                categories,
                subcategories,
                llm_model=small_llm_model,
                temperature=temperature,
                language=language
            )

            return {
                'cluster_id': cluster_id,
                'canonical_question': canonical_q.content.strip(),
                'consolidated_answer': consolidated_a.content.strip(),
                'category': merged_category,
                'subcategory': merged_subcategory,
                'source_count': 1,
                'questions': questions,
                'answers': answers,
                'source_categories': categories,
                'source_subcategories': subcategories
            }

        # Initialize LLM
        llm = ChatOpenAI(model=llm_model, temperature=temperature)

        # Prepare documents for questions (ensure all are strings)
        question_docs = [Document(page_content=str(q)) for q in questions if pd.notna(q)]

        # Create prompt for question summarization with FAQ context
        question_prompt = PromptTemplate.from_template(
            "You are creating a FAQ (Frequently Asked Questions) entry. Your task is to analyze these similar user questions and create a single, clear, canonical question that would be appropriate for a FAQ section.\n\n"
            "The canonical question should:\n"
            "- Be professionally worded and clear\n"
            "- Capture the common intent of all the questions\n"
            "- Be phrased as a typical FAQ question that users would expect to find\n"
            "- Be concise but comprehensive\n"
            f"- Be in {language} language\n\n"
            "Similar user questions:\n\n"
            "{context}\n\n"
            "Generate a canonical FAQ question:"
        )

        # Create chain for question summarization
        question_chain = create_stuff_documents_chain(llm, question_prompt)

        # Run question summarization using async ainvoke
        canonical_q = await question_chain.ainvoke({"context": question_docs})

        # Prepare documents for answers (ensure all are strings)
        answer_docs = [Document(page_content=str(a)) for a in answers if pd.notna(a)]

        # Create prompt for answer consolidation with canonical question context
        answer_prompt = PromptTemplate.from_template(
            "You are creating a FAQ (Frequently Asked Questions) entry. Your task is to write a comprehensive answer for the following FAQ question by consolidating the provided source answers.\n\n"
            "FAQ Question: {canonical_question}\n\n"
            "Your answer should:\n"
            "- Directly address the FAQ question above\n"
            "- Be written in a professional, informational FAQ style\n"
            "- Consolidate all relevant information from the source answers\n"
            "- Remove redundancy while preserving important details\n"
            "- Be clear, concise, and actionable\n"
            "- Be structured and easy to read\n"
            "- Focus on providing information, not conversation\n"
            f"- Be in {language} language\n\n"
            "IMPORTANT: Do NOT include:\n"
            "- Summary sections\n"
            "- Phrases like 'feel free to ask', 'don't hesitate to contact', 'further assistance'\n"
            "- Conversational elements or chatbot-style language\n"
            "- Invitations for follow-up questions\n"
            "- Any concluding statements about getting help\n\n"
            "Source answers to consolidate:\n"
            "{context}\n\n"
            "Generate a direct, informational FAQ answer:"
        )

        # Create chain for answer consolidation
        answer_chain = create_stuff_documents_chain(llm, answer_prompt)

        # Run answer consolidation using async ainvoke with canonical question
        consolidated_a = await answer_chain.ainvoke({
            "context": answer_docs,
            "canonical_question": canonical_q.strip()
        })

        merged_category, merged_subcategory = await summarize_categories(
            questions[:5] if len(questions) > 5 else questions,
            categories,
            subcategories,
            llm_model=small_llm_model,
            temperature=temperature,
            language=language
        )

        logger.info(f"Successfully summarized cluster {cluster_id}")
        return {
            'cluster_id': cluster_id,
            'canonical_question': canonical_q.strip(),
            'consolidated_answer': consolidated_a.strip(),
            'category': merged_category,
            'subcategory': merged_subcategory,
            'source_count': len(questions),
            'questions': questions,
            'answers': answers,
            'source_categories': categories,
            'source_subcategories': subcategories
        }


class CategoryModel(BaseModel):
    reasoning: str
    category: str
    subcategory: str


async def summarize_categories(
    questions: List[str],
    categories: List[str],
    subcategories: List[str],
    llm_model: str = 'gpt-4.1-mini',
    temperature: float = 0.5,
    language: str = 'French',
) -> Tuple[str, str]:
    """
    Summarize a list of categories and subcategories into a single category and subcategory.
    """
    llm = ChatOpenAI(model=llm_model, temperature=temperature)
    formatted_questions = "\n\n".join(questions)
    prompt = PromptTemplate.from_template(
        "You are creating a FAQ (Frequently Asked Questions) categories entry. "
        "Your task is to analyze these categories and subcategories and create a single, clear, merged category and subcategory.\n"
        "A couple of question examples:\n"
        "---\n"
        f"{formatted_questions}\n\n"
        "---\n"
        "Categories:\n"
        "{categories}\n\n"
        "Subcategories:\n"
        "{subcategories}\n\n"
        "Generate a merged consolidated category and subcategory.\n"
        f"Merged category should be clear and concise and be written in {language} language."
        "Output format: JSON with reasoning, category and subcategory keys."
    )
    formatted_prompt = prompt.format(categories=categories, subcategories=subcategories)
    response = await llm.with_structured_output(CategoryModel).ainvoke(formatted_prompt)
    logger.info(f"Summarized categories {categories} -> {response.category}")
    logger.info(f"Summarized subcategories {subcategories} -> {response.subcategory}")
    logger.info(f"Reasoning: {response.reasoning}")
    merged_category, merged_subcategory = response.category, response.subcategory
    return merged_category, merged_subcategory


def compute_category_embeddings(categories: List[str]) -> np.ndarray:
    """
    Compute OpenAI embeddings for a list of categories or subcategories.
    """
    logger.info(f"Computing embeddings for {len(categories)} categories/subcategories")

    # Filter out empty/invalid categories
    valid_categories = []
    for cat in categories:
        if pd.isna(cat) or not cat or str(cat).strip() == '':
            valid_categories.append("Unknown")  # Default for empty categories
        else:
            valid_categories.append(str(cat).strip())

    if not valid_categories:
        logger.error("No valid categories to embed")
        return np.array([])

    embedder = OpenAIEmbeddings()
    embeddings = embedder.embed_documents(valid_categories)
    logger.info(f"Successfully computed category embeddings with shape: {np.array(embeddings).shape}")
    return np.array(embeddings)


def find_nearest_neighbors(embeddings: np.ndarray, k: int = 3) -> Tuple[np.ndarray, np.ndarray]:
    """
    Find k nearest neighbors for each embedding using cosine distance.

    Returns:
        neighbor_indices: Array of shape (n_samples, k) with indices of nearest neighbors
        neighbor_distances: Array of shape (n_samples, k) with distances to nearest neighbors
    """
    logger.info(f"Finding {k} nearest neighbors for {len(embeddings)} embeddings")

    # Compute cosine distance matrix
    distance_matrix = cosine_distances(embeddings)

    # For each point, find k+1 nearest neighbors (including itself)
    # Then exclude the point itself (distance 0) to get k actual neighbors
    neighbor_indices = np.argsort(distance_matrix, axis=1)[:, 1:k+1]  # Skip first (self)

    # Get the corresponding distances
    neighbor_distances = np.array([
        distance_matrix[i, neighbor_indices[i]]
        for i in range(len(embeddings))
    ])

    logger.info(f"Found nearest neighbors with average distances: {np.mean(neighbor_distances, axis=0)}")
    return neighbor_indices, neighbor_distances


def get_most_common_or_shortest(candidates: List[str]) -> str:
    """
    Get the most common category from candidates, or the shortest if no clear winner.
    """
    if not candidates:
        return "Unknown"

    # Remove empty/invalid candidates
    valid_candidates = [c for c in candidates if c and str(c).strip() != '']
    if not valid_candidates:
        return "Unknown"

    # Count occurrences
    counter = Counter(valid_candidates)
    most_common = counter.most_common()

    # If there's a clear winner (appears more than once or only one unique value)
    if len(most_common) == 1 or most_common[0][1] > most_common[1][1]:
        return most_common[0][0]

    # If all have the same count, return the shortest
    max_count = most_common[0][1]
    tied_candidates = [item[0] for item in most_common if item[1] == max_count]
    return min(tied_candidates, key=len)


def apply_refinement_logic(
    categories: List[str],
    neighbor_indices: np.ndarray,
    neighbor_distances: np.ndarray,
    far_threshold: float = 0.7,
    close_threshold: float = 0.3
) -> List[str]:
    """
    Apply the refinement logic based on neighbor distances.

    Args:
        categories: Original categories
        neighbor_indices: Indices of nearest neighbors
        neighbor_distances: Distances to nearest neighbors
        far_threshold: If top-2, top-3 distances are above this, keep original
        close_threshold: If top-2, top-3 distances are below this, apply refinement

    Returns:
        Refined categories
    """
    logger.info(f"Applying refinement logic with far_threshold={far_threshold}, close_threshold={close_threshold}")

    refined_categories = []

    for i, (original_cat, neighbors_idx, neighbors_dist) in enumerate(
        zip(categories, neighbor_indices, neighbor_distances)
    ):
        # Check if we have at least 2 neighbors for analysis
        if len(neighbors_dist) < 2:
            refined_categories.append(original_cat)
            continue

        # Get top-2 and top-3 distances (closest neighbors have smallest distances)
        top_2_dist = neighbors_dist[1] if len(neighbors_dist) > 1 else neighbors_dist[0]
        top_3_dist = neighbors_dist[2] if len(neighbors_dist) > 2 else top_2_dist

        # If distances are too far, keep original
        if top_2_dist > far_threshold and top_3_dist > far_threshold:
            refined_categories.append(original_cat)
            logger.debug(f"Keeping original category '{original_cat}' (distances too far: {top_2_dist:.3f}, {top_3_dist:.3f})")
            continue

        # If distances are close enough, analyze neighbors
        if top_2_dist <= close_threshold or top_3_dist <= close_threshold:
            # Get neighbor categories (including the original)
            neighbor_categories = [original_cat]
            for neighbor_idx in neighbors_idx[:3]:  # Top 3 neighbors
                if neighbor_idx < len(categories):
                    neighbor_categories.append(categories[neighbor_idx])

            # Apply selection logic
            refined_cat = get_most_common_or_shortest(neighbor_categories)
            refined_categories.append(refined_cat)

            if refined_cat != original_cat:
                logger.debug(f"Refined category '{original_cat}' -> '{refined_cat}' (distances: {neighbors_dist[:3]})")
            else:
                logger.debug(f"Kept category '{original_cat}' after analysis (distances: {neighbors_dist[:3]})")
        else:
            # In between thresholds - keep original
            refined_categories.append(original_cat)
            logger.debug(f"Keeping original category '{original_cat}' (intermediate distances: {top_2_dist:.3f}, {top_3_dist:.3f})")

    return refined_categories


def refine_categories_and_subcategories(
    faq_df: pd.DataFrame,
    category_far_threshold: float = 0.7,
    category_close_threshold: float = 0.3,
    subcategory_far_threshold: float = 0.7,
    subcategory_close_threshold: float = 0.3
) -> pd.DataFrame:
    """
    Refine categories and subcategories using embedding-based similarity analysis.

    Args:
        faq_df: DataFrame with FAQ entries containing 'category' and 'subcategory' columns
        category_far_threshold: Distance threshold above which categories are considered too far
        category_close_threshold: Distance threshold below which categories are considered close
        subcategory_far_threshold: Distance threshold above which subcategories are considered too far
        subcategory_close_threshold: Distance threshold below which subcategories are considered close

    Returns:
        DataFrame with refined 'category' and 'subcategory' columns
    """
    logger.info("Starting category and subcategory refinement stage")

    if faq_df.empty:
        logger.warning("Empty FAQ DataFrame provided for refinement")
        return faq_df

    # Make a copy to avoid modifying the original
    refined_df = faq_df.copy()

    # Refine categories
    if 'category' in refined_df.columns:
        logger.info("Refining categories...")
        categories = refined_df['category'].tolist()
        unique_categories = list(set(categories))

        if len(unique_categories) > 1:  # Only refine if there are multiple categories
            # Compute embeddings for unique categories
            category_embeddings = compute_category_embeddings(unique_categories)

            if len(category_embeddings) > 0:
                # Find nearest neighbors
                neighbor_indices, neighbor_distances = find_nearest_neighbors(category_embeddings, k=3)

                # Apply refinement logic
                refined_unique_categories = apply_refinement_logic(
                    unique_categories,
                    neighbor_indices,
                    neighbor_distances,
                    far_threshold=category_far_threshold,
                    close_threshold=category_close_threshold
                )

                # Create mapping from original to refined categories
                category_mapping = dict(zip(unique_categories, refined_unique_categories))

                # Apply mapping to all categories in the DataFrame
                refined_df['category'] = refined_df['category'].map(category_mapping)

                # Log refinement results
                changes = sum(1 for orig, refined in category_mapping.items() if orig != refined)
                logger.info(f"Category refinement: {changes}/{len(unique_categories)} categories changed")
            else:
                logger.warning("Failed to compute category embeddings")
        else:
            logger.info("Only one unique category found, skipping category refinement")

    # Refine subcategories
    if 'subcategory' in refined_df.columns:
        logger.info("Refining subcategories...")
        subcategories = refined_df['subcategory'].tolist()
        unique_subcategories = list(set(subcategories))

        if len(unique_subcategories) > 1:  # Only refine if there are multiple subcategories
            # Compute embeddings for unique subcategories
            subcategory_embeddings = compute_category_embeddings(unique_subcategories)

            if len(subcategory_embeddings) > 0:
                # Find nearest neighbors
                neighbor_indices, neighbor_distances = find_nearest_neighbors(subcategory_embeddings, k=3)

                # Apply refinement logic
                refined_unique_subcategories = apply_refinement_logic(
                    unique_subcategories,
                    neighbor_indices,
                    neighbor_distances,
                    far_threshold=subcategory_far_threshold,
                    close_threshold=subcategory_close_threshold
                )

                # Create mapping from original to refined subcategories
                subcategory_mapping = dict(zip(unique_subcategories, refined_unique_subcategories))

                # Apply mapping to all subcategories in the DataFrame
                refined_df['subcategory'] = refined_df['subcategory'].map(subcategory_mapping)

                # Log refinement results
                changes = sum(1 for orig, refined in subcategory_mapping.items() if orig != refined)
                logger.info(f"Subcategory refinement: {changes}/{len(unique_subcategories)} subcategories changed")
            else:
                logger.warning("Failed to compute subcategory embeddings")
        else:
            logger.info("Only one unique subcategory found, skipping subcategory refinement")

    logger.info("Category and subcategory refinement completed")
    return refined_df


async def build_faq(
    df: pd.DataFrame,
    embed_method: str = 'openai',
    cluster_method: str = 'hdbscan',
    cluster_kwargs: Dict[str, Any] = None,
    llm_model: str = 'gpt-4.1',
    expected_clusters: int = None,
    max_workers: int = 8,
    language: str = 'French',
    enable_refinement: bool = True,
    category_far_threshold: float = 0.7,
    category_close_threshold: float = 0.3,
    subcategory_far_threshold: float = 0.7,
    subcategory_close_threshold: float = 0.3,
) -> pd.DataFrame:
    """
    Async version of build_faq with concurrent cluster summarization and category refinement.

    Args:
        df: Input DataFrame with columns 'question', 'answer', 'created_at', 'rating', 'answer_relevancy', 'topic', 'topic_group'
        embed_method: 'openai' (default) or 'local' (using sentence-transformers)
        cluster_method: 'hdbscan', 'agglomerative', or 'kmeans'
        cluster_kwargs: Additional parameters for the clustering algorithm
        llm_model: OpenAI model for summarization
        expected_clusters: Expected number of clusters (helps optimize clustering parameters)
        max_workers: Maximum number of concurrent LLM calls for cluster summarization
        language: Language of the FAQ (default: 'French')
        enable_refinement: Whether to enable category and subcategory refinement stage
        category_far_threshold: Distance threshold above which categories are considered too far for refinement
        category_close_threshold: Distance threshold below which categories are considered close for refinement
        subcategory_far_threshold: Distance threshold above which subcategories are considered too far for refinement
        subcategory_close_threshold: Distance threshold below which subcategories are considered close for refinement

    Returns a DataFrame with columns:
      'cluster_id', 'canonical_question', 'consolidated_answer', 'source_count', 'category', 'subcategory'
    """
    logger.info("Starting async FAQ building pipeline")

    # Validate input data
    if df.empty:
        logger.warning("Empty DataFrame provided")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    # Filter out rows with invalid questions before processing
    original_len = len(df)
    df_clean = df.dropna(subset=['question']).copy()
    df_clean = df_clean[df_clean['question'].astype(str).str.strip() != '']

    if len(df_clean) < original_len:
        logger.warning(f"Filtered out {original_len - len(df_clean)} rows with invalid questions")

    if df_clean.empty:
        logger.error("No valid questions remaining after filtering")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    # 1. Compute embeddings (synchronous)
    questions = df_clean['question'].astype(str).tolist()
    embeddings = compute_embeddings(questions)

    # 2. Cluster (synchronous)
    cluster_kwargs = cluster_kwargs or {}
    labels = cluster_embeddings(
        embeddings,
        method=cluster_method,
        expected_clusters=expected_clusters,
        **cluster_kwargs
    )
    df_clean['cluster_id'] = labels

    # 3. Prepare cluster data for async processing
    unique_labels = sorted(set(labels))
    valid_clusters = [label for label in unique_labels if label >= 0]  # Skip noise clusters

    if not valid_clusters:
        logger.warning("No valid clusters found")
        return pd.DataFrame(columns=['cluster_id', 'canonical_question', 'consolidated_answer', 'source_count'])

    logger.info(f"Processing {len(valid_clusters)} clusters with up to {max_workers} concurrent workers")

    # Create semaphore to limit concurrent LLM calls
    semaphore = asyncio.Semaphore(max_workers)

    # Prepare tasks for async processing
    tasks = []
    for cluster_id in valid_clusters:
        sub = df_clean[df_clean['cluster_id'] == cluster_id]

        # Sort by available quality metrics (handle missing columns gracefully)
        sort_columns = []
        sort_ascending = []

        for col, ascending in [('rating', False), ('answer_relevancy', False), ('created_at', False)]:
            if col in sub.columns:
                sort_columns.append(col)
                sort_ascending.append(ascending)

        if sort_columns:
            sub = sub.sort_values(by=sort_columns, ascending=sort_ascending)

        qs = sub['question'].tolist()
        ans = sub['answer'].tolist()
        cats = sub['topic_group'].tolist()
        subcats = sub['topic'].tolist()

        # Create async task for this cluster
        task = summarize_cluster(
            cluster_id=cluster_id,
            questions=qs,
            answers=ans,
            categories=cats,
            subcategories=subcats,
            llm_model=llm_model,
            temperature=0.5,
            semaphore=semaphore,
            language=language
        )
        tasks.append(task)

    # 4. Execute all cluster summarization tasks concurrently
    logger.info(f"Starting concurrent processing of {len(tasks)} clusters...")
    start_time = asyncio.get_event_loop().time()

    results = await asyncio.gather(*tasks)

    end_time = asyncio.get_event_loop().time()
    processing_time = end_time - start_time

    logger.info(f"Completed async processing in {processing_time:.2f} seconds")
    logger.info(f"Generated {len(results)} FAQ entries from {len(valid_clusters)} clusters")

    # Filter out any empty results
    valid_results = [r for r in results if r['canonical_question'] and r['consolidated_answer']]

    faq_df = pd.DataFrame(valid_results)

    # 5. Apply category and subcategory refinement stage
    if enable_refinement and not faq_df.empty:
        logger.info("Starting category and subcategory refinement stage")
        faq_df = refine_categories_and_subcategories(
            faq_df,
            category_far_threshold=category_far_threshold,
            category_close_threshold=category_close_threshold,
            subcategory_far_threshold=subcategory_far_threshold,
            subcategory_close_threshold=subcategory_close_threshold
        )
        logger.info("Category and subcategory refinement stage completed")
    elif not enable_refinement:
        logger.info("Category and subcategory refinement stage skipped (disabled)")

    return faq_df


def parse_args():
    parser = argparse.ArgumentParser(description="Generate FAQ from Q&A DataFrame.")

    parser.add_argument('--input', required=True, help='Path to input CSV file')
    parser.add_argument('--output', required=True, help='Path to output CSV file')
    parser.add_argument('--limit', type=int, help='Limit number of rows to process')
    parser.add_argument('--api_key', help='OpenAI API key')
    parser.add_argument(
        '--cluster_method', choices=['hdbscan', 'agglomerative', 'kmeans'], default='kmeans',
        help='Clustering method to use (HDBSCAN always uses cosine distance, optimal for OpenAI embeddings)'
    )
    parser.add_argument(
        '--cluster_kwargs', default='{}',
        help='JSON string of clustering parameters (e.g., \'{"min_cluster_size": 5}\')'
    )
    parser.add_argument(
        '--expected_clusters', type=int, default=None,
        help='Expected number of clusters (helps optimize clustering parameters)'
    )
    parser.add_argument(
        '--llm_model', default='gpt-4.1',
        help='OpenAI model version for summarization'
    )
    parser.add_argument(
        '--max_workers', type=int, default=8,
        help='Maximum number of concurrent LLM calls for cluster summarization (default: 8)'
    )
    parser.add_argument(
        '--language', default='French', choices=['French', 'English', 'Russian', 'Korean', 'Spanish'],
        help='Language of the FAQ (default: French)'
    )
    parser.add_argument(
        '--verbose', action='store_true',
        help='Enable verbose logging'
    )

    # Category and subcategory refinement arguments
    parser.add_argument(
        '--disable_refinement', action='store_true',
        help='Disable category and subcategory refinement stage'
    )
    parser.add_argument(
        '--category_far_threshold', type=float, default=0.7,
        help='Distance threshold above which categories are considered too far for refinement (default: 0.7)'
    )
    parser.add_argument(
        '--category_close_threshold', type=float, default=0.3,
        help='Distance threshold below which categories are considered close for refinement (default: 0.3)'
    )
    parser.add_argument(
        '--subcategory_far_threshold', type=float, default=0.7,
        help='Distance threshold above which subcategories are considered too far for refinement (default: 0.7)'
    )
    parser.add_argument(
        '--subcategory_close_threshold', type=float, default=0.3,
        help='Distance threshold below which subcategories are considered close for refinement (default: 0.3)'
    )

    return parser.parse_args()


async def main():
    """Main async function that handles command line arguments and runs the FAQ building process."""
    # Setup logging first
    setup_logging()

    args = parse_args()
    # Set logging level based on verbose flag
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    logger.info("Starting FAQ building process")
    logger.info(f"Input file: {args.input}")
    logger.info(f"Output file: {args.output}")
    logger.info(f"Clustering method: {args.cluster_method}")
    logger.info(f"LLM model: {args.llm_model}")
    logger.info(f"Max workers: {args.max_workers}")
    logger.info(f"Category refinement enabled: {not args.disable_refinement}")
    if not args.disable_refinement:
        logger.info(f"Category thresholds - far: {args.category_far_threshold}, close: {args.category_close_threshold}")
        logger.info(f"Subcategory thresholds - far: {args.subcategory_far_threshold}, close: {args.subcategory_close_threshold}")
    logger.info("Using async processing with concurrent cluster summarization")

    if args.api_key:
        # Set up OpenAI API key globally
        original_api_key = os.environ.get('OPENAI_API_KEY')
        os.environ['OPENAI_API_KEY'] = args.api_key

    df = load_data(args.input)

    cluster_kwargs = json.loads(args.cluster_kwargs)
    logger.info(f"Cluster parameters: {cluster_kwargs}")

    # Preprocess and filter data (only if these columns exist)
    count = len(df)
    if 'answer' in df.columns:
        df = df[df['answer'] != 'i-do-not-even-know-what-to-answer-to-this-44']
    if 'grade_selector' in df.columns:
        df = df[df['grade_selector'] == 'Correct answer']

    logger.info(f"Got {len(df)} records after preprocessing")
    if args.limit:
        df = df[:args.limit]

    logger.info(f"Filtered {count - len(df)} records")

    faq_df = await build_faq(
        df,
        cluster_method=args.cluster_method,
        cluster_kwargs=cluster_kwargs,
        llm_model=args.llm_model,
        expected_clusters=args.expected_clusters,
        max_workers=args.max_workers,
        language=args.language,
        enable_refinement=not args.disable_refinement,
        category_far_threshold=args.category_far_threshold,
        category_close_threshold=args.category_close_threshold,
        subcategory_far_threshold=args.subcategory_far_threshold,
        subcategory_close_threshold=args.subcategory_close_threshold
    )

    faq_df.to_csv(args.output, index=False)

    logger.info(f"FAQ with {len(faq_df)} entries written to {args.output}")
    print(f"Successfully generated FAQ with {len(faq_df)} entries")
    print(f"Output saved to: {args.output}")


if __name__ == '__main__':
    asyncio.run(main())
